# 商品榜单数据同步功能说明

## 功能概述

本功能实现了从Elasticsearch索引中同步商品榜单数据到数据库的完整解决方案，支持周榜、月榜和季度榜的自动同步，并提供历史记录追踪功能。

## 核心功能

### 1. 榜单类型支持
- **周榜（WEEKLY）**: 最近7天平台热门商品，每天6点更新
- **月榜（MONTHLY）**: 最近30天平台热门商品，每周一6点更新  
- **季度榜（QUARTERLY）**: 最近4个季度的热销商品榜单，每个新季度6点更新
  - 当前季度（CURRENT_QUARTER）
  - 上1季度（LAST_QUARTER_1）
  - 上2季度（LAST_QUARTER_2）
  - 上3季度（LAST_QUARTER_3）

### 2. 数据同步逻辑
- 从ES索引获取榜单数据（goods_id、hot、rank、type等字段）
- 将当前在榜商品同步到`chance_goods_template`表中
- 商品详细信息从商品表中查询获取
- 定期自动同步，确保数据实时性

### 3. 历史记录功能
- 新增`chance_goods_template_history`表记录榜单变化
- 追踪商品进入和退出榜单的时间戳
- 支持榜单变化历史查询和分析

## 技术架构

### 核心类结构

```
com.voghion.product.core
├── RankingDataSyncCoreService              # 榜单同步服务接口
└── impl
    └── RankingDataSyncCoreServiceImpl       # 榜单同步服务实现

com.voghion.product.model
├── po
│   └── ChanceGoodsTemplateHistory          # 历史记录实体类
├── dto
│   └── RankingDataDTO                      # 榜单数据传输对象
└── enums
    ├── RankingTypeEnums                    # 榜单类型枚举
    └── RankingOperationTypeEnums           # 操作类型枚举

com.voghion.product.admin
├── job
│   └── RankingDataSyncJob                  # 定时任务类
└── controller
    └── RankingDataSyncController           # 手动同步控制器
```

### 数据库表设计

#### chance_goods_template_history 表
```sql
CREATE TABLE `chance_goods_template_history` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `template_id` bigint(20) DEFAULT NULL COMMENT '机会商品模板ID',
  `goods_id` bigint(20) NOT NULL COMMENT '商品ID',
  `ranking_type` int(11) NOT NULL COMMENT '榜单类型',
  `operation_type` int(11) NOT NULL COMMENT '操作类型 1-进入榜单, 2-退出榜单',
  `rank` int(11) DEFAULT NULL COMMENT '排名',
  `hot` int(11) DEFAULT NULL COMMENT '热度值',
  -- 其他字段...
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

## 定时任务配置

### 1. Spring @Scheduled 注解
```java
@Scheduled(cron = "0 0 6 * * ?")        // 每天6点 - 周榜
@Scheduled(cron = "0 0 6 ? * MON")       // 每周一6点 - 月榜  
@Scheduled(cron = "0 0 6 1 1,4,7,10 ?")  // 每季度第一天6点 - 季度榜
```

### 2. XXL-Job 支持
- `syncWeeklyRanking` - 同步周榜
- `syncMonthlyRanking` - 同步月榜
- `syncQuarterlyRanking` - 同步季度榜
- `cleanExpiredRankingData` - 清理过期数据

## API接口

### 手动同步接口
```
POST /ranking/sync/weekly      # 同步周榜
POST /ranking/sync/monthly     # 同步月榜
POST /ranking/sync/quarterly   # 同步季度榜
POST /ranking/sync/all         # 同步所有榜单
POST /ranking/sync/type/{type} # 同步指定类型榜单
```

### 查询接口
```
GET /ranking/sync/types           # 获取榜单类型列表
GET /ranking/sync/count/{type}    # 获取在榜商品数量
```

## 配置说明

### 1. ES索引配置
```java
// 在RankingDataSyncCoreServiceImpl中配置ES索引名称
private static final String RANKING_INDEX = "goods_ranking_index";
```

### 2. 榜单类型映射
```java
// ChanceGoodsTemplate.sourceType 字段映射
8  - 周榜
9  - 月榜
10 - 当前季度
11 - 上1季度
12 - 上2季度
13 - 上3季度
```

## 使用方法

### 1. 自动同步
系统会根据配置的定时任务自动执行同步：
- 每天6点同步周榜数据
- 每周一6点同步月榜数据
- 每季度第一天6点同步季度榜数据

### 2. 手动同步
通过API接口或管理后台手动触发同步：
```bash
# 同步周榜
curl -X POST http://localhost:8080/ranking/sync/weekly

# 同步月榜
curl -X POST http://localhost:8080/ranking/sync/monthly

# 同步季度榜
curl -X POST http://localhost:8080/ranking/sync/quarterly
```

### 3. 监控和日志
- 所有同步操作都有详细的日志记录
- 支持通过历史表查询榜单变化情况
- 提供同步状态和错误信息反馈

## 错误处理

### 1. 数据校验
- 验证ES返回数据的完整性
- 检查商品是否存在于商品表中
- 处理数据类型转换异常

### 2. 事务管理
- 使用`@Transactional`确保数据一致性
- 同步失败时自动回滚
- 支持部分成功的处理策略

### 3. 异常恢复
- 记录详细的错误日志
- 支持手动重试机制
- 提供数据修复工具

## 性能优化

### 1. 批量操作
- 使用批量插入和更新减少数据库交互
- 分页处理大量数据避免内存溢出

### 2. 索引优化
- 为关键查询字段添加数据库索引
- 优化ES查询条件和返回字段

### 3. 缓存策略
- 缓存商品基础信息减少重复查询
- 使用Redis缓存热点数据

## 扩展功能

### 1. 数据分析
- 榜单变化趋势分析
- 商品热度变化追踪
- 类目榜单分布统计

### 2. 告警机制
- 同步失败告警
- 数据异常告警
- 性能监控告警

### 3. 管理界面
- 榜单数据可视化展示
- 同步任务管理界面
- 历史记录查询功能

## 注意事项

1. **ES索引结构**: 确保ES索引包含必要的字段（goods_id、hot、rank、type等）
2. **数据一致性**: 同步过程中注意数据的一致性和完整性
3. **性能影响**: 大量数据同步时注意对系统性能的影响
4. **监控告警**: 建议配置监控告警确保同步任务正常运行
5. **数据备份**: 重要数据变更前建议进行备份

## 维护指南

### 1. 日常维护
- 定期检查同步任务执行状态
- 监控数据库存储空间使用情况
- 清理过期的历史记录数据

### 2. 故障排查
- 检查ES连接状态和索引结构
- 验证数据库表结构和索引
- 查看应用日志定位问题原因

### 3. 版本升级
- 注意数据库表结构变更
- 验证ES索引字段兼容性
- 测试新版本功能完整性
