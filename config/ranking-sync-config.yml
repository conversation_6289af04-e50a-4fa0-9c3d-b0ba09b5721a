# 榜单数据同步配置文件

# Elasticsearch 配置
elasticsearch:
  # 榜单数据索引名称
  ranking-index: "goods_ranking_index"
  # 连接超时时间（毫秒）
  connection-timeout: 10000
  # 查询超时时间（毫秒）
  query-timeout: 30000

# 榜单同步配置
ranking:
  sync:
    # 是否启用自动同步
    enabled: true
    
    # 批量处理大小
    batch-size: 1000
    
    # 最大重试次数
    max-retry: 3
    
    # 重试间隔（毫秒）
    retry-interval: 5000
    
    # 数据保留天数
    data-retention-days: 30
    
    # 周榜配置
    weekly:
      # 是否启用
      enabled: true
      # 同步时间（cron表达式）
      cron: "0 0 6 * * ?"
      # 数据范围（天）
      data-range-days: 7
      
    # 月榜配置
    monthly:
      # 是否启用
      enabled: true
      # 同步时间（cron表达式）
      cron: "0 0 6 ? * MON"
      # 数据范围（天）
      data-range-days: 30
      
    # 季度榜配置
    quarterly:
      # 是否启用
      enabled: true
      # 同步时间（cron表达式）
      cron: "0 0 6 1 1,4,7,10 ?"
      # 数据范围（季度数）
      data-range-quarters: 4

# 定时任务配置
xxl:
  job:
    # 榜单同步任务配置
    ranking-sync:
      # 任务超时时间（秒）
      timeout: 3600
      # 失败重试次数
      fail-retry-count: 2
      # 任务分片参数
      sharding-param: ""

# 监控告警配置
monitoring:
  # 是否启用监控
  enabled: true
  
  # 告警配置
  alert:
    # 同步失败告警
    sync-failure:
      enabled: true
      # 钉钉机器人webhook
      dingtalk-webhook: "https://oapi.dingtalk.com/robot/send?access_token=xxx"
      
    # 数据异常告警
    data-anomaly:
      enabled: true
      # 数据量变化阈值（百分比）
      threshold: 20

# 日志配置
logging:
  level:
    com.voghion.product.core.impl.RankingDataSyncCoreServiceImpl: INFO
    com.voghion.product.admin.job.RankingDataSyncJob: INFO
