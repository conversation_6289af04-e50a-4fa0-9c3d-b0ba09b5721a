#!/bin/bash

# 商品榜单数据同步功能部署脚本
# 作者: system
# 日期: 2024-12-19

set -e

echo "=========================================="
echo "开始部署商品榜单数据同步功能"
echo "=========================================="

# 配置变量
DB_HOST=${DB_HOST:-"localhost"}
DB_PORT=${DB_PORT:-"3306"}
DB_NAME=${DB_NAME:-"voghion_product"}
DB_USER=${DB_USER:-"root"}
DB_PASSWORD=${DB_PASSWORD:-""}

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查数据库连接
check_database() {
    log_info "检查数据库连接..."
    
    if command -v mysql &> /dev/null; then
        mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -e "SELECT 1;" "$DB_NAME" &> /dev/null
        if [ $? -eq 0 ]; then
            log_info "数据库连接成功"
        else
            log_error "数据库连接失败，请检查配置"
            exit 1
        fi
    else
        log_warn "未找到mysql命令，跳过数据库连接检查"
    fi
}

# 创建数据库表
create_tables() {
    log_info "创建数据库表..."
    
    if [ -f "sql/chance_goods_template_history.sql" ]; then
        mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" < sql/chance_goods_template_history.sql
        if [ $? -eq 0 ]; then
            log_info "数据库表创建成功"
        else
            log_error "数据库表创建失败"
            exit 1
        fi
    else
        log_error "未找到SQL文件: sql/chance_goods_template_history.sql"
        exit 1
    fi
}

# 编译项目
compile_project() {
    log_info "编译项目..."
    
    if command -v mvn &> /dev/null; then
        mvn clean compile -DskipTests
        if [ $? -eq 0 ]; then
            log_info "项目编译成功"
        else
            log_error "项目编译失败"
            exit 1
        fi
    else
        log_warn "未找到maven命令，跳过编译"
    fi
}

# 运行测试
run_tests() {
    log_info "运行测试..."
    
    if command -v mvn &> /dev/null; then
        mvn test -Dtest=RankingDataSyncTest
        if [ $? -eq 0 ]; then
            log_info "测试运行成功"
        else
            log_warn "测试运行失败，请检查"
        fi
    else
        log_warn "未找到maven命令，跳过测试"
    fi
}

# 配置XXL-Job任务
configure_xxl_job() {
    log_info "配置XXL-Job任务..."
    
    cat << EOF
请在XXL-Job管理界面中添加以下任务：

1. 周榜同步任务
   - 任务名称: syncWeeklyRanking
   - Cron表达式: 0 0 6 * * ?
   - 执行器: voghion-product-admin
   - JobHandler: syncWeeklyRanking
   - 描述: 每天6点同步周榜数据

2. 月榜同步任务
   - 任务名称: syncMonthlyRanking
   - Cron表达式: 0 0 6 ? * MON
   - 执行器: voghion-product-admin
   - JobHandler: syncMonthlyRanking
   - 描述: 每周一6点同步月榜数据

3. 季度榜同步任务
   - 任务名称: syncQuarterlyRanking
   - Cron表达式: 0 0 6 1 1,4,7,10 ?
   - 执行器: voghion-product-admin
   - JobHandler: syncQuarterlyRanking
   - 描述: 每季度第一天6点同步季度榜数据

4. 清理过期数据任务
   - 任务名称: cleanExpiredRankingData
   - Cron表达式: 0 0 2 * * ?
   - 执行器: voghion-product-admin
   - JobHandler: cleanExpiredRankingData
   - 描述: 每天凌晨2点清理过期榜单数据
EOF
}

# 验证部署
verify_deployment() {
    log_info "验证部署..."
    
    # 检查API接口
    if command -v curl &> /dev/null; then
        log_info "测试API接口..."
        
        # 测试获取榜单类型列表
        response=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8080/ranking/sync/types)
        if [ "$response" = "200" ]; then
            log_info "API接口测试成功"
        else
            log_warn "API接口测试失败，HTTP状态码: $response"
        fi
    else
        log_warn "未找到curl命令，跳过API测试"
    fi
    
    # 检查数据库表
    table_exists=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" -e "SHOW TABLES LIKE 'chance_goods_template_history';" | wc -l)
    if [ "$table_exists" -gt 1 ]; then
        log_info "数据库表验证成功"
    else
        log_error "数据库表验证失败"
    fi
}

# 主函数
main() {
    log_info "开始部署流程..."
    
    # 检查必要的环境
    check_database
    
    # 创建数据库表
    create_tables
    
    # 编译项目
    compile_project
    
    # 运行测试
    run_tests
    
    # 配置XXL-Job任务
    configure_xxl_job
    
    # 验证部署
    verify_deployment
    
    log_info "部署完成！"
    
    echo ""
    echo "=========================================="
    echo "部署后续步骤："
    echo "1. 配置ES索引映射"
    echo "2. 在XXL-Job中添加定时任务"
    echo "3. 配置监控告警"
    echo "4. 执行首次手动同步测试"
    echo "=========================================="
}

# 执行主函数
main "$@"
