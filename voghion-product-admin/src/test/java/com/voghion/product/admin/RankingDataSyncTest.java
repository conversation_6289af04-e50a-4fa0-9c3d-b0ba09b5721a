package com.voghion.product.admin;

import com.voghion.product.core.RankingDataSyncCoreService;
import com.voghion.product.model.enums.RankingTypeEnums;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * 榜单数据同步功能测试类
 * 
 * <AUTHOR>
 * @since 2024-12-19
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class RankingDataSyncTest {

    @Resource
    private RankingDataSyncCoreService rankingDataSyncCoreService;

    /**
     * 测试同步周榜数据
     */
    @Test
    public void testSyncWeeklyRanking() {
        log.info("开始测试同步周榜数据");
        try {
            rankingDataSyncCoreService.syncWeeklyRanking();
            log.info("周榜数据同步测试成功");
        } catch (Exception e) {
            log.error("周榜数据同步测试失败", e);
        }
    }

    /**
     * 测试同步月榜数据
     */
    @Test
    public void testSyncMonthlyRanking() {
        log.info("开始测试同步月榜数据");
        try {
            rankingDataSyncCoreService.syncMonthlyRanking();
            log.info("月榜数据同步测试成功");
        } catch (Exception e) {
            log.error("月榜数据同步测试失败", e);
        }
    }

    /**
     * 测试同步季度榜数据
     */
    @Test
    public void testSyncQuarterlyRanking() {
        log.info("开始测试同步季度榜数据");
        try {
            rankingDataSyncCoreService.syncQuarterlyRanking();
            log.info("季度榜数据同步测试成功");
        } catch (Exception e) {
            log.error("季度榜数据同步测试失败", e);
        }
    }

    /**
     * 测试获取当前在榜商品数量
     */
    @Test
    public void testGetCurrentRankingGoodsIds() {
        log.info("开始测试获取当前在榜商品数量");
        try {
            for (RankingTypeEnums rankingType : RankingTypeEnums.values()) {
                int count = rankingDataSyncCoreService.getCurrentRankingGoodsIds(rankingType).size();
                log.info("{}当前在榜商品数量: {}", rankingType.getName(), count);
            }
            log.info("获取在榜商品数量测试成功");
        } catch (Exception e) {
            log.error("获取在榜商品数量测试失败", e);
        }
    }

    /**
     * 测试同步指定类型榜单
     */
    @Test
    public void testSyncRankingByType() {
        log.info("开始测试同步指定类型榜单");
        try {
            // 测试同步周榜
            rankingDataSyncCoreService.syncRankingByType(RankingTypeEnums.WEEKLY);
            log.info("指定类型榜单同步测试成功");
        } catch (Exception e) {
            log.error("指定类型榜单同步测试失败", e);
        }
    }
}
