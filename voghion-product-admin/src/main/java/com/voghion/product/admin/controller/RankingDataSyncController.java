package com.voghion.product.admin.controller;

import com.colorlight.base.model.BaseResultCode;
import com.colorlight.base.model.Result;
import com.colorlight.base.model.constants.NoLogin;
import com.voghion.product.VoghionProductResultCode;
import com.voghion.product.core.RankingDataSyncCoreService;
import com.voghion.product.model.enums.RankingTypeEnums;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 榜单数据同步控制器
 * 
 * <AUTHOR>
 * @since 2024-12-19
 */
@RestController
@RequestMapping("/ranking/sync")
@Api(tags = "榜单数据同步管理")
@Slf4j
public class RankingDataSyncController {

    @Resource
    private RankingDataSyncCoreService rankingDataSyncCoreService;

    /**
     * 手动同步周榜数据
     */
    @PostMapping("/weekly")
    @NoLogin
    @ApiOperation("手动同步周榜数据")
    public Result<Boolean> syncWeeklyRanking() {
        try {
            rankingDataSyncCoreService.syncWeeklyRanking();
            log.info("周榜数据同步成功");
            return Result.success(true);
        } catch (Exception e) {
            log.error("手动同步周榜数据失败", e);
            return Result.fail(VoghionProductResultCode.SYSTEM_ERROR.getCode(), "周榜数据同步失败: " + e.getMessage());
        }
    }

    /**
     * 手动同步月榜数据
     */
    @PostMapping("/monthly")
    @NoLogin
    @ApiOperation("手动同步月榜数据")
    public Result<Boolean> syncMonthlyRanking() {
        try {
            rankingDataSyncCoreService.syncMonthlyRanking();
            log.info("月榜数据同步成功");
            return Result.success(true);
        } catch (Exception e) {
            log.error("手动同步月榜数据失败", e);
            return Result.fail(VoghionProductResultCode.SYSTEM_ERROR.getCode(),"月榜数据同步失败: " + e.getMessage());
        }
    }

    /**
     * 手动同步季度榜数据
     */
    @PostMapping("/quarterly")
    @NoLogin
    @ApiOperation("手动同步季度榜数据")
    public Result<Boolean> syncQuarterlyRanking() {
        try {
            rankingDataSyncCoreService.syncQuarterlyRanking();
            log.info("季度榜数据同步成功");
            return Result.success(true);
        } catch (Exception e) {
            log.error("手动同步季度榜数据失败", e);
            return Result.fail(VoghionProductResultCode.SYSTEM_ERROR.getCode(),"季度榜数据同步失败: " + e.getMessage());
        }
    }

    /**
     * 同步指定类型的榜单数据
     */
    @PostMapping("/type/{rankingType}")
    @NoLogin
    @ApiOperation("同步指定类型的榜单数据")
    public Result<Boolean> syncRankingByType(
            @ApiParam(value = "榜单类型代码", required = true, example = "8")
            @PathVariable Integer rankingType) {
        try {
            RankingTypeEnums typeEnum = RankingTypeEnums.getByCode(rankingType);
            if (typeEnum == null) {
                return Result.fail(VoghionProductResultCode.SYSTEM_ERROR.getCode(),"无效的榜单类型: " + rankingType);
            }
            
            rankingDataSyncCoreService.syncRankingByType(typeEnum);
            log.info(typeEnum.getName() + "数据同步成功");
            return Result.success(true);
        } catch (Exception e) {
            log.error("同步指定类型榜单数据失败, type: {}", rankingType, e);
            return Result.fail(VoghionProductResultCode.SYSTEM_ERROR.getCode(),"榜单数据同步失败: " + e.getMessage());
        }
    }

    /**
     * 同步所有类型的榜单数据
     */
    @PostMapping("/all")
    @NoLogin
    @ApiOperation("同步所有类型的榜单数据")
    public Result<Boolean> syncAllRankings() {
        try {
            rankingDataSyncCoreService.syncWeeklyRanking();
            rankingDataSyncCoreService.syncMonthlyRanking();
            rankingDataSyncCoreService.syncQuarterlyRanking();
            log.info("所有榜单数据同步成功");
            return Result.success(true);
        } catch (Exception e) {
            log.error("同步所有榜单数据失败", e);
            return Result.fail(VoghionProductResultCode.SYSTEM_ERROR.getCode(),"榜单数据同步失败: " + e.getMessage());
        }
    }

    /**
     * 获取榜单类型列表
     */
    @GetMapping("/types")
    @NoLogin
    @ApiOperation("获取榜单类型列表")
    public Result<RankingTypeEnums[]> getRankingTypes() {
        return Result.success(RankingTypeEnums.values());
    }

    /**
     * 获取当前在榜商品数量
     */
    @GetMapping("/count/{rankingType}")
    @NoLogin
    @ApiOperation("获取当前在榜商品数量")
    public Result<Integer> getCurrentRankingCount(
            @ApiParam(value = "榜单类型代码", required = true, example = "8")
            @PathVariable Integer rankingType) {
        try {
            RankingTypeEnums typeEnum = RankingTypeEnums.getByCode(rankingType);
            if (typeEnum == null) {
                return Result.fail(VoghionProductResultCode.SYSTEM_ERROR.getCode(),"无效的榜单类型: " + rankingType);
            }
            
            int count = rankingDataSyncCoreService.getCurrentRankingGoodsIds(typeEnum).size();
            log.info("获取在榜商品数量成功");
            return Result.success(count);
        } catch (Exception e) {
            log.error("获取在榜商品数量失败, type: {}", rankingType, e);
            return Result.fail(VoghionProductResultCode.SYSTEM_ERROR.getCode(),"获取在榜商品数量失败: " + e.getMessage());
        }
    }
}
