package com.voghion.product.admin.job;

import com.voghion.product.util.LogUtils;
import com.voghion.product.core.RankingDataSyncCoreService;
import com.voghion.product.model.enums.RankingTypeEnums;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;

/**
 * 榜单数据同步定时任务
 * 
 * <AUTHOR>
 * @since 2024-12-19
 */
@Component
@Slf4j
public class RankingDataSyncJob {

    @Resource
    private RankingDataSyncCoreService rankingDataSyncCoreService;

    /**
     * 同步周榜数据
     * 每天6点更新最近7天平台热门商品
     */
    @Scheduled(cron = "0 0 6 * * ?")
    @XxlJob("syncWeeklyRanking")
    public void syncWeeklyRanking() {
        LogUtils.info(log, "开始执行周榜数据同步定时任务 - {}", 
                     LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        
        try {
            rankingDataSyncCoreService.syncWeeklyRanking();
            LogUtils.info(log, "周榜数据同步定时任务执行成功");
        } catch (Exception e) {
            LogUtils.error(log, "周榜数据同步定时任务执行失败", e);
            throw e;
        }
    }

    /**
     * 同步月榜数据
     * 每周一6点更新最近30天平台热门商品
     */
    @Scheduled(cron = "0 0 6 ? * MON")
    @XxlJob("syncMonthlyRanking")
    public void syncMonthlyRanking() {
        LogUtils.info(log, "开始执行月榜数据同步定时任务 - {}", 
                     LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        
        try {
            rankingDataSyncCoreService.syncMonthlyRanking();
            LogUtils.info(log, "月榜数据同步定时任务执行成功");
        } catch (Exception e) {
            LogUtils.error(log, "月榜数据同步定时任务执行失败", e);
            throw e;
        }
    }

    /**
     * 同步季度榜数据
     * 每个新季度的第一天6点更新最近4个季度的热销商品榜单
     */
    @Scheduled(cron = "0 0 6 1 1,4,7,10 ?")
    @XxlJob("syncQuarterlyRanking")
    public void syncQuarterlyRanking() {
        LogUtils.info(log, "开始执行季度榜数据同步定时任务 - {}", 
                     LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        
        try {
            rankingDataSyncCoreService.syncQuarterlyRanking();
            LogUtils.info(log, "季度榜数据同步定时任务执行成功");
        } catch (Exception e) {
            LogUtils.error(log, "季度榜数据同步定时任务执行失败", e);
            throw e;
        }
    }

    /**
     * 清理过期榜单数据
     * 每天凌晨2点执行，清理30天前的历史数据
     */
    @Scheduled(cron = "0 0 2 * * ?")
    @XxlJob("cleanExpiredRankingData")
    public void cleanExpiredRankingData() {
        LogUtils.info(log, "开始执行过期榜单数据清理定时任务 - {}", 
                     LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        
        try {
            // 计算30天前的日期
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.DAY_OF_MONTH, -30);
            Date beforeDate = calendar.getTime();
            
            // 清理各类型榜单的过期数据
            for (RankingTypeEnums rankingType : RankingTypeEnums.values()) {
                rankingDataSyncCoreService.cleanExpiredRankingData(rankingType, beforeDate);
            }
            
            LogUtils.info(log, "过期榜单数据清理定时任务执行成功");
        } catch (Exception e) {
            LogUtils.error(log, "过期榜单数据清理定时任务执行失败", e);
            throw e;
        }
    }

    /**
     * 手动触发周榜同步（用于测试）
     */
    @XxlJob("manualSyncWeeklyRanking")
    public void manualSyncWeeklyRanking() {
        LogUtils.info(log, "手动触发周榜数据同步");
        syncWeeklyRanking();
    }

    /**
     * 手动触发月榜同步（用于测试）
     */
    @XxlJob("manualSyncMonthlyRanking")
    public void manualSyncMonthlyRanking() {
        LogUtils.info(log, "手动触发月榜数据同步");
        syncMonthlyRanking();
    }

    /**
     * 手动触发季度榜同步（用于测试）
     */
    @XxlJob("manualSyncQuarterlyRanking")
    public void manualSyncQuarterlyRanking() {
        LogUtils.info(log, "手动触发季度榜数据同步");
        syncQuarterlyRanking();
    }

    /**
     * 同步指定类型的榜单数据（通过参数指定）
     */
    @XxlJob("syncRankingByType")
    public void syncRankingByType() {
        // 可以通过XXL-Job的参数传递榜单类型
        // 这里简化处理，实际使用时可以通过XxlJobHelper.getJobParam()获取参数
        LogUtils.info(log, "通过参数同步指定类型榜单数据");
        
        try {
            // 示例：同步所有类型
            for (RankingTypeEnums rankingType : RankingTypeEnums.values()) {
                rankingDataSyncCoreService.syncRankingByType(rankingType);
            }
            LogUtils.info(log, "指定类型榜单数据同步完成");
        } catch (Exception e) {
            LogUtils.error(log, "指定类型榜单数据同步失败", e);
            throw e;
        }
    }
}
