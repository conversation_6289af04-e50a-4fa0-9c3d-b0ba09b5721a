-- 机会商品模板历史记录表
-- 用于追踪商品进入和退出榜单的时间戳

CREATE TABLE `chance_goods_template_history` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `template_id` bigint(20) DEFAULT NULL COMMENT '机会商品模板ID',
  `goods_id` bigint(20) NOT NULL COMMENT '商品ID',
  `ranking_type` int(11) NOT NULL COMMENT '榜单类型 8-周榜, 9-月榜, 10-当前季度, 11-上1季度, 12-上2季度, 13-上3季度',
  `operation_type` int(11) NOT NULL COMMENT '操作类型 1-进入榜单, 2-退出榜单',
  `rank` int(11) DEFAULT NULL COMMENT '排名',
  `hot` int(11) DEFAULT NULL COMMENT '热度值',
  `goods_name` varchar(500) DEFAULT NULL COMMENT '商品名称',
  `category_id` bigint(20) DEFAULT NULL COMMENT '类目ID',
  `main_image` varchar(500) DEFAULT NULL COMMENT '商品主图',
  `min_price` decimal(10,2) DEFAULT NULL COMMENT '最低价',
  `max_price` decimal(10,2) DEFAULT NULL COMMENT '最高价',
  `seven_sales` int(11) DEFAULT NULL COMMENT '近七日销量',
  `operation_time` datetime NOT NULL COMMENT '操作时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(1000) DEFAULT NULL COMMENT '备注信息',
  `is_del` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否删除 0-否 1-是',
  PRIMARY KEY (`id`),
  KEY `idx_goods_id` (`goods_id`),
  KEY `idx_template_id` (`template_id`),
  KEY `idx_ranking_type` (`ranking_type`),
  KEY `idx_operation_type` (`operation_type`),
  KEY `idx_operation_time` (`operation_time`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_is_del` (`is_del`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='机会商品模板历史记录表';

-- 为现有的chance_goods_template表添加索引（如果不存在）
-- ALTER TABLE `chance_goods_template` ADD INDEX `idx_source_type` (`source_type`);
-- ALTER TABLE `chance_goods_template` ADD INDEX `idx_goods_id` (`goods_id`);
-- ALTER TABLE `chance_goods_template` ADD INDEX `idx_is_del` (`is_del`);
-- ALTER TABLE `chance_goods_template` ADD INDEX `idx_status` (`status`);
-- ALTER TABLE `chance_goods_template` ADD INDEX `idx_update_time` (`update_time`);
