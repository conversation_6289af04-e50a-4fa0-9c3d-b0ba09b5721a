package com.voghion.product.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.voghion.product.model.po.specifications.GoodsSpecificationsDuplicateTmpPo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/25 11:34
 */
public interface GoodsSpecificationsDuplicateTmpMapper extends BaseMapper<GoodsSpecificationsDuplicateTmpPo> {

    int saveDuplicateKeyUpdate(@Param("tmpPos") List<GoodsSpecificationsDuplicateTmpPo> specificationsDuplicateTmpPos);

}
