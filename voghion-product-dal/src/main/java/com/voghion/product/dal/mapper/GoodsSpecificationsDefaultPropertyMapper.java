package com.voghion.product.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.voghion.product.model.po.specifications.GoodsSpecificationsDefaultPropertyPo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/3 10:45
 */
public interface GoodsSpecificationsDefaultPropertyMapper extends BaseMapper<GoodsSpecificationsDefaultPropertyPo> {

    int batchUpdateGoodsSpecificationsReferencedCount(@Param("defaultProperties") List<GoodsSpecificationsDefaultPropertyPo> defaultProperties);

}
