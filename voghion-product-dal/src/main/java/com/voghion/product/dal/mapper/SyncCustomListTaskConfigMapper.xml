<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.voghion.product.dal.mapper.SyncCustomListTaskConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.voghion.product.model.po.SyncCustomListTaskConfig">
        <id column="id" property="id" />
        <result column="task_name" property="taskName" />
        <result column="category_id" property="categoryId" />
        <result column="goods_create_start_time" property="goodsCreateStartTime" />
        <result column="goods_create_end_time" property="goodsCreateEndTime" />
        <result column="activity_id" property="activityId" />
        <result column="activity_max_cost" property="activityMaxCost" />
        <result column="min_price" property="minPrice" />
        <result column="max_price" property="maxPrice" />
        <result column="shop_id" property="shopId" />
        <result column="country" property="country" />
        <result column="tagIds" property="tagIds" />
        <result column="run_days" property="runDays" />
        <result column="deal_cnt" property="dealCnt" />
        <result column="deal_uv" property="dealUv" />
        <result column="deal_pay_money" property="dealPayMoney" />
        <result column="click_rate_uv" property="clickRateUv" />
        <result column="add_rate_uv" property="addRateUv" />
        <result column="deal_rate_uv" property="dealRateUv" />
        <result column="ecpm" property="ecpm" />
        <result column="operator" property="operator" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="show_uv_start" property="showUvStart" />
        <result column="show_uv_end" property="showUvEnd" />
        <result column="click_uv" property="clickUv" />
        <result column="add_uv" property="addUv" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, task_name, category_id, goods_create_start_time, goods_create_end_time, activity_id, activity_max_cost, min_price, max_price, shop_id, country, tagIds, run_days, deal_cnt, deal_uv, deal_pay_money, click_rate_uv, add_rate_uv, deal_rate_uv, ecpm, operator, create_time, update_time, show_uv_start, show_uv_end, click_uv, add_uv
    </sql>

</mapper>
