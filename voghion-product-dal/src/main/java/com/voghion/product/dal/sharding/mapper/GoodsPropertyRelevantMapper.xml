<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.voghion.product.dal.sharding.mapper.GoodsPropertyRelevantMapper">

    <sql id="Base_Column_List">
        id, goods_id, property_alias_name, property_value_alias_name, global_property_id, global_property_value_id, name_sort, value_sort, deleted, create_time, update_time, main_property
    </sql>

    <select id="findListByGoodsId" resultType="com.voghion.product.model.po.goods.GoodsPropertyRelevantPo">
        SELECT <include refid="Base_Column_List"/>
        FROM goods_property_relevant
        WHERE goods_id = #{goodsId}
          AND deleted = false
    </select>

</mapper>
