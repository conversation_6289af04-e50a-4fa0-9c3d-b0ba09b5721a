<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.voghion.product.dal.mapper.TongdunAlternativeResultMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.voghion.product.model.po.TongdunAlternativeResult">
        <id column="id" property="id" />
        <result column="goods_id" property="goodsId" />
        <result column="illegal_detail" property="illegalDetail" />
        <result column="category_name" property="categoryName" />
        <result column="audit_result" property="auditResult" />
        <result column="image_details" property="imageDetails" />
        <result column="audit_date" property="auditDate" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, goods_id, illegal_detail, category_name, audit_result, image_details, audit_date
    </sql>

</mapper>
