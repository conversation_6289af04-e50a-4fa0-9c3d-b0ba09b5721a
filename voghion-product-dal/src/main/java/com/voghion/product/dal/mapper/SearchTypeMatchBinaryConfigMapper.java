package com.voghion.product.dal.mapper;

import com.voghion.product.model.po.SearchTypeMatchBinaryConfig;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 类型筛选项二进制配置表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-14
 */
public interface SearchTypeMatchBinaryConfigMapper extends BaseMapper<SearchTypeMatchBinaryConfig> {
    int batchInsert(@Param("list") List<SearchTypeMatchBinaryConfig> configs);
}
