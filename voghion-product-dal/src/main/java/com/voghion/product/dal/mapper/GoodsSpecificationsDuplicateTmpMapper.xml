<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.voghion.product.dal.mapper.GoodsSpecificationsDuplicateTmpMapper">

    <insert id="saveDuplicateKeyUpdate">
        INSERT INTO goods_specifications_duplicate_tmp(category_id, category_name, property_name, property_value, referenced_count)
        VAlUES
        <foreach item="item" collection="tmpPos" separator=",">
            (#{item.categoryId},#{item.categoryName},#{item.propertyName},#{item.propertyValue},#{item.referencedCount})
        </foreach>
        ON DUPLICATE KEY UPDATE referenced_count = values(referenced_count) + referenced_count
    </insert>

</mapper>
