package com.voghion.product.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.voghion.product.model.po.coldStorage.ColdStorageGoodsExtConfig;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 商品拓展配置表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-09
 */
public interface ColdStorageGoodsExtConfigMapper extends BaseMapper<ColdStorageGoodsExtConfig> {

    void backUp(@Param("goodsIds") List<Long> goodsIds);

    void restore(@Param("goodsIds") List<Long> goodsIds);
}
