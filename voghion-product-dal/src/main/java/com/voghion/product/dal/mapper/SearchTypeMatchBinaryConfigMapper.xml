<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.voghion.product.dal.mapper.SearchTypeMatchBinaryConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.voghion.product.model.po.SearchTypeMatchBinaryConfig">
        <id column="id" property="id" />
        <result column="type" property="type" />
        <result column="value" property="value" />
        <result column="create_time" property="createTime" />
        <result column="enum_0" property="enum0" />
        <result column="enum_1" property="enum1" />
        <result column="enum_2" property="enum2" />
        <result column="enum_3" property="enum3" />
        <result column="enum_4" property="enum4" />
        <result column="enum_5" property="enum5" />
        <result column="enum_6" property="enum6" />
        <result column="enum_7" property="enum7" />
        <result column="enum_8" property="enum8" />
        <result column="enum_9" property="enum9" />
        <result column="enum_10" property="enum10" />
        <result column="enum_11" property="enum11" />
        <result column="enum_12" property="enum12" />
        <result column="enum_13" property="enum13" />
        <result column="enum_14" property="enum14" />
        <result column="enum_15" property="enum15" />
        <result column="enum_16" property="enum16" />
        <result column="enum_17" property="enum17" />
        <result column="enum_18" property="enum18" />
        <result column="enum_19" property="enum19" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, type, value, create_time, enum_0, enum_1, enum_2, enum_3, enum_4, enum_5, enum_6, enum_7, enum_8, enum_9, enum_10, enum_11, enum_12, enum_13, enum_14, enum_15, enum_16, enum_17, enum_18, enum_19
    </sql>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT IGNORE INTO search_type_match_binary_config
        (type, value, enum_0, enum_1, enum_2, enum_3, enum_4, enum_5, enum_6, enum_7, enum_8, enum_9, enum_10, enum_11, enum_12, enum_13, enum_14, enum_15, enum_16, enum_17, enum_18, enum_19)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.type},
            #{item.value},
            #{item.enum0},
            #{item.enum1},
            #{item.enum2},
            #{item.enum3},
            #{item.enum4},
            #{item.enum5},
            #{item.enum6},
            #{item.enum7},
            #{item.enum8},
            #{item.enum9},
            #{item.enum10},
            #{item.enum11},
            #{item.enum12},
            #{item.enum13},
            #{item.enum14},
            #{item.enum15},
            #{item.enum16},
            #{item.enum17},
            #{item.enum18},
            #{item.enum19}
            )
        </foreach>
    </insert>

</mapper>
