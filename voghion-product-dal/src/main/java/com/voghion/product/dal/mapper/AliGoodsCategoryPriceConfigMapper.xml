<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.voghion.product.dal.mapper.AliGoodsCategoryPriceConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.voghion.product.model.po.AliGoodsCategoryPriceConfig">
        <id column="id" property="id" />
        <result column="second_category_id" property="secondCategoryId" />
        <result column="default_weigh" property="defaultWeigh" />
        <result column="start_price" property="startPrice" />
        <result column="end_price" property="endPrice" />
        <result column="freight" property="freight" />
        <result column="profit" property="profit" />
        <result column="multiple" property="multiple" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, second_category_id, default_weigh, start_price, end_price, freight, profit, multiple, create_time
    </sql>

</mapper>
