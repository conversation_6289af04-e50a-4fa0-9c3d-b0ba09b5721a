package com.voghion.product.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.voghion.product.model.po.coldStorage.ColdStorageGoodsExtDetail;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 商品扩展表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-27
 */
public interface ColdStorageGoodsExtDetailMapper extends BaseMapper<ColdStorageGoodsExtDetail> {

    void backUp(@Param("goodsIds") List<Long> goodsIds);

    void restore(@Param("goodsIds") List<Long> goodsIds);
}
