<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.voghion.product.dal.mapper.GoodsChannelAssociationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.voghion.product.model.po.GoodsChannelAssociation">
        <id column="id" property="id" />
        <result column="goods_id" property="goodsId" />
        <result column="sku_id" property="skuId" />
        <result column="original_price" property="originalPrice" />
        <result column="name" property="name" />
        <result column="original_shop_id" property="originalShopId" />
        <result column="original_sku_id" property="originalSkuId" />
        <result column="original_goods_id" property="originalGoodsId" />
        <result column="original_sku_name" property="originalSkuName" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="status" property="status" />
        <result column="channel" property="channel" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, goods_id, sku_id, original_price, name, original_shop_id, original_sku_id, original_goods_id, original_sku_name, create_time, update_time, status, channel
    </sql>

</mapper>
