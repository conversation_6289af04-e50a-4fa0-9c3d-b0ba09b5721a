<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.voghion.product.dal.sharding.mapper.PropertyAssociationMapper">

    <sql id="all_field">
        id, goods_id, property_id, property_value_id, property_relevant_id, deleted, create_time, update_time
    </sql>

    <select id="findListByGoodsId" resultType="com.voghion.product.model.po.goods.PropertyAssociationPo">
        select <include refid="all_field"/>
        from property_association
        where goods_id = #{goodsId}
        and deleted = false
    </select>

</mapper>
