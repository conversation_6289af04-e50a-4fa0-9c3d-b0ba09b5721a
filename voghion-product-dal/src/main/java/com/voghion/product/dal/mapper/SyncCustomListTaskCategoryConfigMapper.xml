<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.voghion.product.dal.mapper.SyncCustomListTaskCategoryConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.voghion.product.model.po.SyncCustomListTaskCategoryConfig">
        <id column="id" property="id" />
        <result column="file_id" property="fileId" />
        <result column="category_type" property="categoryType" />
        <result column="category_id" property="categoryId" />
        <result column="min_price_start" property="minPriceStart" />
        <result column="min_price_end" property="minPriceEnd" />
        <result column="max_price_start" property="maxPriceStart" />
        <result column="max_price_end" property="maxPriceEnd" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, file_id, category_type, category_id, min_price_start, min_price_end, max_price_start, max_price_end, create_time
    </sql>

</mapper>
