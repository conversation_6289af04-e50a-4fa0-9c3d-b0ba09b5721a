package com.voghion.product.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.voghion.product.model.po.coldStorage.ColdStorageGoodsFreight;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-16
 */
public interface ColdStorageGoodsFreightMapper extends BaseMapper<ColdStorageGoodsFreight> {

    void backUp(@Param("goodsIds") List<Long> goodsIds);

    void restore(@Param("goodsIds") List<Long> goodsIds);
}
