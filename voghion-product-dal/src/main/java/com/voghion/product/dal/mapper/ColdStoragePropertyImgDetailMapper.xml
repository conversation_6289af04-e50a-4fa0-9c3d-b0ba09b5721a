<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.voghion.product.dal.mapper.ColdStoragePropertyImgDetailMapper">


    <insert id="backUp">
        INSERT INTO property_img_detail_back_up
        (select * from property_img_detail where spu_id in
            <foreach collection="productIds" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
        );
    </insert>

    <insert id="restore">
        INSERT INTO property_img_detail_back_up
        (select * from property_img_detail where spu_id in
            <foreach collection="productIds" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
        );
    </insert>
</mapper>
