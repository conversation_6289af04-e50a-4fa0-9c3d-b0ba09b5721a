<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.voghion.product.dal.mapper.ChanceGoodsTemplateHistoryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.voghion.product.model.po.ChanceGoodsTemplateHistory">
        <id column="id" property="id" />
        <result column="template_id" property="templateId" />
        <result column="goods_id" property="goodsId" />
        <result column="ranking_type" property="rankingType" />
        <result column="operation_type" property="operationType" />
        <result column="rank" property="rank" />
        <result column="hot" property="hot" />
        <result column="goods_name" property="goodsName" />
        <result column="category_id" property="categoryId" />
        <result column="main_image" property="mainImage" />
        <result column="min_price" property="minPrice" />
        <result column="max_price" property="maxPrice" />
        <result column="seven_sales" property="sevenSales" />
        <result column="operation_time" property="operationTime" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="remark" property="remark" />
        <result column="is_del" property="isDel" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, template_id, goods_id, ranking_type, operation_type, rank, hot, goods_name, 
        category_id, main_image, min_price, max_price, seven_sales, operation_time, 
        create_time, update_time, remark, is_del
    </sql>

</mapper>
