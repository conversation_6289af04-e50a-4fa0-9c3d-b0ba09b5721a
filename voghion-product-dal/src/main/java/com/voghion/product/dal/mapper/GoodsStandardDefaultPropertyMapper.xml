<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.voghion.product.dal.mapper.GoodsStandardDefaultPropertyMapper">

    <update id="batchUpdateReferencedCount">
        UPDATE goods_standard_default_property
        SET referenced_count = CASE id
        <foreach collection="defaultProperties" item="property">
            WHEN #{property.id} THEN referenced_count + #{property.referencedCount}
        </foreach>
        END
        WHERE id IN
        <foreach collection="defaultProperties" item="property" open="(" separator="," close=")">
            #{property.id}
        </foreach>

    </update>

</mapper>
