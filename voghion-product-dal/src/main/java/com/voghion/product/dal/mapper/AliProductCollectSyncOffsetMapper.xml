<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.voghion.product.dal.mapper.AliProductCollectSyncOffsetMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.voghion.product.model.po.AliProductCollectSyncOffset">
        <id column="id" property="id" />
        <result column="product_collect_id" property="productCollectId" />
        <result column="is_end" property="isEnd" />
        <result column="cur_page" property="curPage" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, product_collect_id, is_end, cur_page, create_time, update_time
    </sql>

</mapper>
