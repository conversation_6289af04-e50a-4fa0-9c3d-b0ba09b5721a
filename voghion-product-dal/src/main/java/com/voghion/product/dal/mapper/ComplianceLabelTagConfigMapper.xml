<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.voghion.product.dal.mapper.ComplianceLabelTagConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.voghion.product.model.po.ComplianceLabelTagConfig">
        <id column="id" property="id" />
        <result column="tag_id" property="tagId" />
        <result column="reason" property="reason" />
        <result column="status_config" property="statusConfig" />
        <result column="is_del" property="isDel" />
        <result column="update_time" property="updateTime" />
        <result column="update_user" property="updateUser" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, tag_id, reason, status_config, is_del, update_time, update_user
    </sql>

</mapper>
