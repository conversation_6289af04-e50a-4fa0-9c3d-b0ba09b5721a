package com.voghion.product.dal.sharding.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.voghion.product.model.po.goods.PropertyAssociationPo;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/27 14:42
 */
public interface PropertyAssociationMapper extends BaseMapper<PropertyAssociationPo> {

    @Options(flushCache = Options.FlushCachePolicy.TRUE)
    List<PropertyAssociationPo> findListByGoodsId(@Param("goodsId") Long goodsId);

}
