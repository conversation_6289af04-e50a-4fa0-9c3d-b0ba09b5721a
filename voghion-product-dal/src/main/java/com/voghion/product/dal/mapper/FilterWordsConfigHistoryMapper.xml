<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.voghion.product.dal.mapper.FilterWordsConfigHistoryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.voghion.product.model.po.FilterWordsConfigHistory">
        <id column="id" property="id" />
        <result column="config_id" property="configId" />
        <result column="update_type" property="updateType" />
        <result column="update_text" property="updateText" />
        <result column="update_before" property="updateBefore" />
        <result column="update_after" property="updateAfter" />
        <result column="update_reason" property="updateReason" />
        <result column="create_time" property="createTime" />
        <result column="create_user" property="createUser" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, config_id, update_type, update_text, update_before, update_after, update_reason, create_time, create_user
    </sql>

</mapper>
