<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.voghion.product.dal.mapper.RecommendGoodsInfoChangeLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.voghion.product.model.po.RecommendGoodsInfoChangeLog">
        <id column="id" property="id" />
        <result column="goods_id" property="goodsId" />
        <result column="category_id" property="categoryId" />
        <result column="shop_id" property="shopId" />
        <result column="type" property="type" />
        <result column="content" property="content" />
        <result column="status" property="status" />
        <result column="apply_id" property="applyId" />
        <result column="is_del" property="isDel" />
        <result column="add_price" property="addPrice" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, goods_id, category_id, shop_id, type, content, status, apply_id, is_del, add_price, create_time, update_time
    </sql>

</mapper>
