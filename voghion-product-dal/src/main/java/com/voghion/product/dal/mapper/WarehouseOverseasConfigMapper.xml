<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.voghion.product.dal.mapper.WarehouseOverseasConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.voghion.product.model.po.WarehouseOverseasConfig">
        <id column="id" property="id" />
        <result column="shop_id" property="shopId" />
        <result column="shop_name" property="shopName" />
        <result column="country" property="country" />
        <result column="deliver_country" property="deliverCountry" />
        <result column="refund_country" property="refundCountry" />
        <result column="province" property="province" />
        <result column="city" property="city" />
        <result column="zipcode" property="zipcode" />
        <result column="min_delivery_period" property="minDeliveryPeriod" />
        <result column="max_delivery_period" property="maxDeliveryPeriod" />
        <result column="refund_address" property="refundAddress" />
        <result column="receiver" property="receiver" />
        <result column="receiver_phone" property="receiverPhone" />
        <result column="sale_status" property="saleStatus" />
        <result column="is_del" property="isDel" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_user" property="createUser" />
        <result column="update_user" property="updateUser" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, shop_id, shop_name, country, deliver_country, refund_country, province, city, zipcode,
        min_delivery_period, max_delivery_period, refund_address, receiver, receiver_phone,
        sale_status, is_del, create_time, update_time, create_user, update_user
    </sql>

</mapper>
