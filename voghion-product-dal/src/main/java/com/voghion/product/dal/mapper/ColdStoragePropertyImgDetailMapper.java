package com.voghion.product.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.voghion.product.model.po.coldStorage.ColdStoragePropertyImgDetail;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 属性变体图关联详情表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-27
 */
public interface ColdStoragePropertyImgDetailMapper extends BaseMapper<ColdStoragePropertyImgDetail> {

    void backUp(@Param("productIds") List<Long> productIds);

    void restore(@Param("productIds") List<Long> productIds);
}
