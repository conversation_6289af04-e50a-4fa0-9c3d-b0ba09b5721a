<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.voghion.product.dal.sharding.mapper.GoodsPropertyImgRelevantMapper">

    <sql id="Base_Column_List">
        id, goods_id, goods_property_relevant_id, kw, img_url, sort, deleted, create_time, update_time
    </sql>

    <select id="findListByGoodsId" resultType="com.voghion.product.model.po.goods.GoodsPropertyImgRelevantPo">
        SELECT <include refid="Base_Column_List"/>
        FROM goods_property_img_relevant
        WHERE goods_id = #{goodsId}
        AND deleted = false
    </select>

</mapper>
