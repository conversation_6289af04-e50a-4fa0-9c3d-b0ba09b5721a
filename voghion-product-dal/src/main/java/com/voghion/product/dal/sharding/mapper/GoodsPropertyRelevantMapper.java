package com.voghion.product.dal.sharding.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.voghion.product.model.po.goods.GoodsPropertyRelevantPo;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/14 16:31
 */
public interface GoodsPropertyRelevantMapper extends BaseMapper<GoodsPropertyRelevantPo> {

    @Options(useCache = false, flushCache = Options.FlushCachePolicy.TRUE)
    List<GoodsPropertyRelevantPo> findListByGoodsId(@Param("goodsId") Long goodsId);

}
