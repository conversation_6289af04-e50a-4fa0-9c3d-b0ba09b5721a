<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.voghion.product.dal.mapper.SyncCustomListTaskRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.voghion.product.model.po.SyncCustomListTaskRecord">
        <id column="id" property="id" />
        <result column="trigger_type" property="triggerType" />
        <result column="custom_id" property="customId" />
        <result column="excute_type" property="excuteType" />
        <result column="task_create_time" property="taskCreateTime" />
        <result column="task_id" property="taskId" />
        <result column="execute_time" property="executeTime" />
        <result column="execute_status" property="executeStatus" />
        <result column="execute_desc" property="executeDesc" />
        <result column="operator" property="operator" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, trigger_type, custom_id, excute_type, task_create_time, task_id, execute_time, execute_status, execute_desc, operator, create_time
    </sql>

</mapper>
