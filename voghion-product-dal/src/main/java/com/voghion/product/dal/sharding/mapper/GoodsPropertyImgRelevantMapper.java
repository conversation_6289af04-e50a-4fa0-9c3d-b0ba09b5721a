package com.voghion.product.dal.sharding.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.voghion.product.model.po.goods.GoodsPropertyImgRelevantPo;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/14 16:33
 */
public interface GoodsPropertyImgRelevantMapper extends BaseMapper<GoodsPropertyImgRelevantPo> {

    @Options(useCache = false, flushCache = Options.FlushCachePolicy.TRUE)
    List<GoodsPropertyImgRelevantPo> findListByGoodsId(@Param("goodsId") Long goodsId);

}
