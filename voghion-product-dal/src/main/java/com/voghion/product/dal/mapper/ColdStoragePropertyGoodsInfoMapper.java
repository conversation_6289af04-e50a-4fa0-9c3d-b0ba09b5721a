package com.voghion.product.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.voghion.product.model.po.coldStorage.ColdStoragePropertyGoodsInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 商品规范表 Mapper 接口
 * </p>
 *
 * <AUTHOR> @since 2022-09-16
 */
public interface ColdStoragePropertyGoodsInfoMapper extends BaseMapper<ColdStoragePropertyGoodsInfo> {

    void backUp(@Param("goodsIds") List<Long> goodsIds);

    void restore(@Param("goodsIds") List<Long> goodsIds);
}
