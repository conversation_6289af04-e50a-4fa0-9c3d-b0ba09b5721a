<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.voghion.product.dal.mapper.ColdStorageGoodsExtDetailImgMapper">


    <insert id="backUp">
        INSERT INTO goods_ext_detail_img_back_up
        (select * from goods_ext_detail_img where goods_id in
        <foreach collection="goodsIds" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
        );
    </insert>

    <insert id="restore">
        INSERT INTO goods_ext_detail_img
        (select * from goods_ext_detail_img_back_up where goods_id in
        <foreach collection="goodsIds" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
        );
    </insert>
</mapper>
