<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.voghion.product.dal.mapper.LogisticsPropertyConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.voghion.product.model.po.LogisticsPropertyConfig">
        <id column="id" property="id" />
        <result column="value" property="value" />
        <result column="frontend_desc" property="frontendDesc" />
        <result column="backend_desc" property="backendDesc" />
        <result column="is_del" property="isDel" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, value, frontend_desc, backend_desc, is_del, create_time, update_time
    </sql>

</mapper>
