package com.voghion.product.core.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.voghion.product.util.LogUtils;
import com.voghion.product.core.RankingDataSyncCoreService;
import com.voghion.product.model.dto.RankingDataDTO;
import com.voghion.product.model.enums.RankingOperationTypeEnums;
import com.voghion.product.model.enums.RankingTypeEnums;
import com.voghion.product.model.po.ChanceGoodsTemplate;
import com.voghion.product.model.po.ChanceGoodsTemplateHistory;
import com.voghion.product.model.po.Goods;
import com.voghion.product.service.ChanceGoodsTemplateHistoryService;
import com.voghion.product.service.ChanceGoodsTemplateService;
import com.voghion.product.service.GoodsService;
import com.voghion.product.service.impl.AbstractCommonServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.opensearch.action.search.SearchRequest;
import org.opensearch.action.search.SearchResponse;
import org.opensearch.client.RequestOptions;
import org.opensearch.client.RestHighLevelClient;
import org.opensearch.index.query.BoolQueryBuilder;
import org.opensearch.index.query.QueryBuilders;
import org.opensearch.search.SearchHit;
import org.opensearch.search.builder.SearchSourceBuilder;
import org.opensearch.search.sort.SortOrder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 榜单数据同步核心服务实现类
 * 
 * <AUTHOR>
 * @since 2024-12-19
 */
@Service
@Slf4j
public class RankingDataSyncCoreServiceImpl extends AbstractCommonServiceImpl implements RankingDataSyncCoreService {

    @Resource
    private ChanceGoodsTemplateService chanceGoodsTemplateService;

    @Resource
    private ChanceGoodsTemplateHistoryService chanceGoodsTemplateHistoryService;

    @Resource
    private GoodsService goodsService;

    @Resource
    private RestHighLevelClient restHighLevelClient;

    /**
     * ES索引名称 - 根据实际项目配置调整
     */
    private static final String RANKING_INDEX = "goods_ranking_index";

    /**
     * 批处理大小 - 用于批量数据库操作
     */
    private static final int BATCH_SIZE = 500;

    @Override
    public void syncWeeklyRanking() {
        LogUtils.info(log, "开始同步周榜数据");
        try {
            syncRankingByType(RankingTypeEnums.WEEKLY);
            LogUtils.info(log, "周榜数据同步完成");
        } catch (Exception e) {
            LogUtils.error(log, "周榜数据同步失败", e);
            throw new RuntimeException("周榜数据同步失败", e);
        }
    }

    @Override
    public void syncMonthlyRanking() {
        LogUtils.info(log, "开始同步月榜数据");
        try {
            syncRankingByType(RankingTypeEnums.MONTHLY);
            LogUtils.info(log, "月榜数据同步完成");
        } catch (Exception e) {
            LogUtils.error(log, "月榜数据同步失败", e);
            throw new RuntimeException("月榜数据同步失败", e);
        }
    }

    @Override
    public void syncQuarterlyRanking() {
        LogUtils.info(log, "开始同步季度榜数据");
        try {
            // 同步最近4个季度的榜单
            syncRankingByType(RankingTypeEnums.CURRENT_QUARTER);
            syncRankingByType(RankingTypeEnums.LAST_QUARTER_1);
            syncRankingByType(RankingTypeEnums.LAST_QUARTER_2);
            syncRankingByType(RankingTypeEnums.LAST_QUARTER_3);
            LogUtils.info(log, "季度榜数据同步完成");
        } catch (Exception e) {
            LogUtils.error(log, "季度榜数据同步失败", e);
            throw new RuntimeException("季度榜数据同步失败", e);
        }
    }

    @Override
    public void syncRankingByType(RankingTypeEnums rankingType) {
        LogUtils.info(log, "开始同步{}榜单数据", rankingType.getName());

        // 1. 获取当前在榜商品列表
        List<Long> currentRankingGoodsIds = getCurrentRankingGoodsIds(rankingType);

        // 2. 从ES获取最新榜单数据
        List<RankingDataDTO> newRankingData = fetchRankingDataFromES(rankingType);

        if (CollectionUtils.isEmpty(newRankingData)) {
            LogUtils.warn(log, "从ES获取{}榜单数据为空", rankingType.getName());
            return;
        }

        // 3. 获取新榜单商品ID列表
        List<Long> newRankingGoodsIds = newRankingData.stream()
                .map(RankingDataDTO::getGoodsId)
                .collect(Collectors.toList());

        // 4. 找出退出榜单的商品
        List<Long> exitGoodsIds = currentRankingGoodsIds.stream()
                .filter(goodsId -> !newRankingGoodsIds.contains(goodsId))
                .collect(Collectors.toList());

        // 5. 找出新进入榜单的商品
        List<Long> enterGoodsIds = newRankingGoodsIds.stream()
                .filter(goodsId -> !currentRankingGoodsIds.contains(goodsId))
                .collect(Collectors.toList());

        // 6. 批量记录退出榜单的历史
        batchRecordExitHistory(exitGoodsIds, rankingType);

        // 7. 同步新榜单数据到模板表
        syncRankingDataToTemplate(newRankingData, rankingType);

        // 8. 批量记录进入榜单的历史
        batchRecordEnterHistory(newRankingData, enterGoodsIds, rankingType);

        LogUtils.info(log, "{}榜单数据同步完成，新增{}个商品，移除{}个商品",
                rankingType.getName(), enterGoodsIds.size(), exitGoodsIds.size());
    }

    @Override
    public List<RankingDataDTO> fetchRankingDataFromES(RankingTypeEnums rankingType) {
        LogUtils.info(log, "从ES获取{}榜单数据", rankingType.getName());

        List<RankingDataDTO> rankingDataList = new ArrayList<>();

        try {
            SearchRequest searchRequest = new SearchRequest(RANKING_INDEX);
            SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();

            // 构建查询条件
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            boolQuery.must(QueryBuilders.termQuery("type", rankingType.getCode()));
            boolQuery.must(QueryBuilders.rangeQuery("hot").gt(0)); // 热度值大于0

            sourceBuilder.query(boolQuery);
            sourceBuilder.sort("rank", SortOrder.ASC); // 按排名升序排列
            sourceBuilder.size(1000); // 限制返回数量

            // 指定返回字段
            sourceBuilder.fetchSource(new String[]{
                    "goods_id", "hot", "rank", "type", "goods_name",
                    "category_id", "main_image", "seven_sales"
            }, null);

            searchRequest.source(sourceBuilder);

            SearchResponse response = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);

            if (response.getHits().getTotalHits().value > 0) {
                for (SearchHit hit : response.getHits().getHits()) {
                    Map<String, Object> sourceMap = hit.getSourceAsMap();

                    RankingDataDTO rankingData = new RankingDataDTO();
                    rankingData.setGoodsId(getLongValue(sourceMap, "goods_id"));
                    rankingData.setHot(getIntegerValue(sourceMap, "hot"));
                    rankingData.setRank(getIntegerValue(sourceMap, "rank"));
                    rankingData.setType(getIntegerValue(sourceMap, "type"));
                    rankingData.setGoodsName(getStringValue(sourceMap, "goods_name"));
                    rankingData.setCategoryId(getLongValue(sourceMap, "category_id"));
                    rankingData.setMainImage(getStringValue(sourceMap, "main_image"));
                    rankingData.setSevenSales(getIntegerValue(sourceMap, "seven_sales"));
                    rankingData.setSourceIndex(RANKING_INDEX);
                    rankingData.setSyncTimestamp(System.currentTimeMillis());

                    rankingDataList.add(rankingData);
                }
            }

            LogUtils.info(log, "从ES获取{}榜单数据{}条", rankingType.getName(), rankingDataList.size());

        } catch (Exception e) {
            LogUtils.error(log, "从ES获取{}榜单数据失败", rankingType.getName(), e);
            throw new RuntimeException("从ES获取榜单数据失败", e);
        }

        return rankingDataList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncRankingDataToTemplate(List<RankingDataDTO> rankingDataList, RankingTypeEnums rankingType) {
        if (CollectionUtils.isEmpty(rankingDataList)) {
            return;
        }

        LogUtils.info(log, "开始同步{}条{}榜单数据到模板表", rankingDataList.size(), rankingType.getName());

        // 1. 先删除该榜单类型的现有数据
        chanceGoodsTemplateService.lambdaUpdate()
                .eq(ChanceGoodsTemplate::getSourceType, rankingType.getCode())
                .set(ChanceGoodsTemplate::getIsDel, 1)
                .set(ChanceGoodsTemplate::getUpdateTime, new Date())
                .update();

        // 2. 批量查询商品信息，避免单条查询
        List<Long> goodsIds = rankingDataList.stream()
                .map(RankingDataDTO::getGoodsId)
                .collect(Collectors.toList());

        Map<Long, Goods> goodsMap = batchQueryGoodsMap(goodsIds);

        // 3. 批量构建模板数据
        List<ChanceGoodsTemplate> templateList = new ArrayList<>();
        Date now = new Date();

        for (RankingDataDTO rankingData : rankingDataList) {
            Goods goods = goodsMap.get(rankingData.getGoodsId());
            if (goods == null) {
                LogUtils.warn(log, "商品ID{}不存在，跳过同步", rankingData.getGoodsId());
                continue;
            }

            ChanceGoodsTemplate template = buildChanceGoodsTemplate(rankingData, goods, rankingType, now);
            templateList.add(template);
        }

        // 4. 批量保存模板数据
        if (!CollectionUtils.isEmpty(templateList)) {
            batchSaveTemplates(templateList);
            LogUtils.info(log, "成功同步{}条{}榜单数据到模板表", templateList.size(), rankingType.getName());
        }
    }

    @Override
    public void recordRankingHistory(Long templateId, Long goodsId, RankingTypeEnums rankingType,
                                     Integer operationType, Integer rank, Integer hot) {
        try {
            ChanceGoodsTemplateHistory history = new ChanceGoodsTemplateHistory();
            history.setTemplateId(templateId);
            history.setGoodsId(goodsId);
            history.setRankingType(rankingType.getCode());
            history.setOperationType(operationType);
            history.setRank(rank);
            history.setHot(hot);
            history.setOperationTime(new Date());
            history.setCreateTime(new Date());
            history.setUpdateTime(new Date());
            history.setIsDel(0);

            // 获取商品信息补充历史记录
            Goods goods = goodsService.getById(goodsId);
            if (goods != null) {
                history.setGoodsName(goods.getName());
                history.setCategoryId(goods.getCategoryId());
                history.setMainImage(goods.getMainImage());
                history.setMinPrice(goods.getMinPrice());
                history.setMaxPrice(goods.getMaxPrice());
            }

            chanceGoodsTemplateHistoryService.save(history);

            LogUtils.info(log, "记录榜单历史：商品ID={}, 榜单类型={}, 操作类型={}, 排名={}",
                    goodsId, rankingType.getName(), operationType, rank);

        } catch (Exception e) {
            LogUtils.error(log, "记录榜单历史失败：商品ID={}, 榜单类型={}", goodsId, rankingType.getName(), e);
        }
    }

    @Override
    public void cleanExpiredRankingData(RankingTypeEnums rankingType, Date beforeDate) {
        LogUtils.info(log, "开始清理{}榜单过期数据，清理{}之前的数据", rankingType.getName(), beforeDate);

        try {
            // 清理过期的模板数据
            chanceGoodsTemplateService.lambdaUpdate()
                    .eq(ChanceGoodsTemplate::getSourceType, rankingType.getCode())
                    .lt(ChanceGoodsTemplate::getUpdateTime, beforeDate)
                    .set(ChanceGoodsTemplate::getIsDel, 1)
                    .set(ChanceGoodsTemplate::getUpdateTime, new Date())
                    .update();

            LogUtils.info(log, "{}榜单过期数据清理完成", rankingType.getName());

        } catch (Exception e) {
            LogUtils.error(log, "清理{}榜单过期数据失败", rankingType.getName(), e);
        }
    }

    @Override
    public List<Long> getCurrentRankingGoodsIds(RankingTypeEnums rankingType) {
        List<ChanceGoodsTemplate> templates = chanceGoodsTemplateService.lambdaQuery()
                .select(ChanceGoodsTemplate::getGoodsId)
                .eq(ChanceGoodsTemplate::getSourceType, rankingType.getCode())
                .eq(ChanceGoodsTemplate::getIsDel, 0)
                .list();

        return templates.stream()
                .map(ChanceGoodsTemplate::getGoodsId)
                .collect(Collectors.toList());
    }

    @Override
    public void batchUpdateTemplateInfo(List<RankingDataDTO> rankingDataList) {
        if (CollectionUtils.isEmpty(rankingDataList)) {
            return;
        }

        LogUtils.info(log, "开始批量更新{}条模板信息", rankingDataList.size());

        List<ChanceGoodsTemplate> updateList = new ArrayList<>();

        for (RankingDataDTO rankingData : rankingDataList) {
            ChanceGoodsTemplate template = chanceGoodsTemplateService.lambdaQuery()
                    .eq(ChanceGoodsTemplate::getGoodsId, rankingData.getGoodsId())
                    .eq(ChanceGoodsTemplate::getSourceType, rankingData.getType())
                    .eq(ChanceGoodsTemplate::getIsDel, 0)
                    .one();

            if (template != null) {
                template.setHot(rankingData.getHot());
                template.setSevenSales(rankingData.getSevenSales());
                template.setUpdateTime(new Date());
                template.setUpdateUser("SYSTEM_RANKING_SYNC");
                updateList.add(template);
            }
        }

        if (!CollectionUtils.isEmpty(updateList)) {
            chanceGoodsTemplateService.updateBatchById(updateList);
            LogUtils.info(log, "批量更新{}条模板信息完成", updateList.size());
        }
    }

    /**
     * 批量记录退出榜单的历史
     */
    private void batchRecordExitHistory(List<Long> exitGoodsIds, RankingTypeEnums rankingType) {
        if (CollectionUtils.isEmpty(exitGoodsIds)) {
            return;
        }

        LogUtils.info(log, "批量记录{}个商品退出{}榜单的历史", exitGoodsIds.size(), rankingType.getName());

        // 批量查询模板信息
        List<ChanceGoodsTemplate> templates = chanceGoodsTemplateService.lambdaQuery()
                .select(ChanceGoodsTemplate::getId, ChanceGoodsTemplate::getGoodsId, ChanceGoodsTemplate::getHot)
                .in(ChanceGoodsTemplate::getGoodsId, exitGoodsIds)
                .eq(ChanceGoodsTemplate::getSourceType, rankingType.getCode())
                .eq(ChanceGoodsTemplate::getIsDel, 0)
                .list();

        if (CollectionUtils.isEmpty(templates)) {
            LogUtils.warn(log, "未找到退出榜单商品的模板信息");
            return;
        }

        // 批量查询商品信息
        Map<Long, Goods> goodsMap = batchQueryGoodsMap(exitGoodsIds);

        // 构建历史记录列表
        List<ChanceGoodsTemplateHistory> historyList = new ArrayList<>();
        Date now = new Date();

        for (ChanceGoodsTemplate template : templates) {
            Goods goods = goodsMap.get(template.getGoodsId());
            ChanceGoodsTemplateHistory history = buildRankingHistory(
                    template.getId(), template.getGoodsId(), goods, rankingType,
                    RankingOperationTypeEnums.EXIT.getCode(), null, template.getHot(), now);
            historyList.add(history);
        }

        // 批量保存历史记录
        batchSaveHistories(historyList);
        LogUtils.info(log, "成功批量记录{}条退出榜单历史", historyList.size());
    }

    /**
     * 批量记录进入榜单的历史
     */
    private void batchRecordEnterHistory(List<RankingDataDTO> newRankingData, List<Long> enterGoodsIds,
                                         RankingTypeEnums rankingType) {
        if (CollectionUtils.isEmpty(enterGoodsIds)) {
            return;
        }

        LogUtils.info(log, "批量记录{}个商品进入{}榜单的历史", enterGoodsIds.size(), rankingType.getName());

        // 构建榜单数据映射
        Map<Long, RankingDataDTO> rankingDataMap = newRankingData.stream()
                .collect(Collectors.toMap(RankingDataDTO::getGoodsId, data -> data));

        // 批量查询新创建的模板信息
        List<ChanceGoodsTemplate> templates = chanceGoodsTemplateService.lambdaQuery()
                .select(ChanceGoodsTemplate::getId, ChanceGoodsTemplate::getGoodsId)
                .in(ChanceGoodsTemplate::getGoodsId, enterGoodsIds)
                .eq(ChanceGoodsTemplate::getSourceType, rankingType.getCode())
                .eq(ChanceGoodsTemplate::getIsDel, 0)
                .list();

        if (CollectionUtils.isEmpty(templates)) {
            LogUtils.warn(log, "未找到进入榜单商品的模板信息");
            return;
        }

        // 批量查询商品信息
        Map<Long, Goods> goodsMap = batchQueryGoodsMap(enterGoodsIds);

        // 构建历史记录列表
        List<ChanceGoodsTemplateHistory> historyList = new ArrayList<>();
        Date now = new Date();

        for (ChanceGoodsTemplate template : templates) {
            RankingDataDTO rankingData = rankingDataMap.get(template.getGoodsId());
            Goods goods = goodsMap.get(template.getGoodsId());

            if (rankingData != null && goods != null) {
                ChanceGoodsTemplateHistory history = buildRankingHistory(
                        template.getId(), template.getGoodsId(), goods, rankingType,
                        RankingOperationTypeEnums.ENTER.getCode(),
                        rankingData.getRank(), rankingData.getHot(), now);
                historyList.add(history);
            }
        }

        // 批量保存历史记录
        batchSaveHistories(historyList);
        LogUtils.info(log, "成功批量记录{}条进入榜单历史", historyList.size());
    }

    /**
     * 从Map中安全获取Long值
     */
    private Long getLongValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value == null) {
            return null;
        }
        if (value instanceof Number) {
            return ((Number) value).longValue();
        }
        try {
            return Long.parseLong(value.toString());
        } catch (NumberFormatException e) {
            LogUtils.warn(log, "无法转换{}为Long类型: {}", key, value);
            return null;
        }
    }

    /**
     * 从Map中安全获取Integer值
     */
    private Integer getIntegerValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value == null) {
            return null;
        }
        if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        try {
            return Integer.parseInt(value.toString());
        } catch (NumberFormatException e) {
            LogUtils.warn(log, "无法转换{}为Integer类型: {}", key, value);
            return null;
        }
    }

    /**
     * 从Map中安全获取String值
     */
    private String getStringValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        return value != null ? value.toString() : null;
    }

    /**
     * 批量查询商品信息并构建Map
     */
    private Map<Long, Goods> batchQueryGoodsMap(List<Long> goodsIds) {
        if (CollectionUtils.isEmpty(goodsIds)) {
            return new HashMap<>();
        }

        Collection<Goods> goodsList = goodsService.listByIds(goodsIds);
        return goodsList.stream()
                .collect(Collectors.toMap(Goods::getId, goods -> goods));
    }

    /**
     * 构建机会商品模板对象
     */
    private ChanceGoodsTemplate buildChanceGoodsTemplate(RankingDataDTO rankingData, Goods goods,
                                                         RankingTypeEnums rankingType, Date now) {
        ChanceGoodsTemplate template = new ChanceGoodsTemplate();
        template.setSourceType(rankingType.getCode());
        template.setSourceDetail(rankingType.getDescription());
        template.setGoodsId(rankingData.getGoodsId());
        template.setGoodsName(goods.getName());
        template.setCategoryId(goods.getCategoryId());
        template.setMainImage(goods.getMainImage());
        template.setHot(rankingData.getHot());
        template.setSevenSales(rankingData.getSevenSales());
        template.setStatus(1); // 开放状态
        template.setGenerateGoodsCount(0);
        template.setIsDel(0);
        template.setCreateTime(now);
        template.setUpdateTime(now);
        template.setCreateUser("SYSTEM_RANKING_SYNC");
        template.setUpdateUser("SYSTEM_RANKING_SYNC");

        // 设置价格信息
        if (goods.getMinPrice() != null) {
            template.setMinPrice(goods.getMinPrice());
        }
        if (goods.getMaxPrice() != null) {
            template.setMaxPrice(goods.getMaxPrice());
        }

        return template;
    }

    /**
     * 构建榜单历史记录对象
     */
    private ChanceGoodsTemplateHistory buildRankingHistory(Long templateId, Long goodsId, Goods goods,
                                                           RankingTypeEnums rankingType, Integer operationType,
                                                           Integer rank, Integer hot, Date now) {
        ChanceGoodsTemplateHistory history = new ChanceGoodsTemplateHistory();
        history.setTemplateId(templateId);
        history.setGoodsId(goodsId);
        history.setRankingType(rankingType.getCode());
        history.setOperationType(operationType);
        history.setRank(rank);
        history.setHot(hot);
        history.setOperationTime(now);
        history.setCreateTime(now);
        history.setUpdateTime(now);
        history.setIsDel(0);

        // 设置商品信息
        if (goods != null) {
            history.setGoodsName(goods.getName());
            history.setCategoryId(goods.getCategoryId());
            history.setMainImage(goods.getMainImage());
            history.setMinPrice(goods.getMinPrice());
            history.setMaxPrice(goods.getMaxPrice());
        }

        return history;
    }

    /**
     * 批量保存模板数据
     */
    private void batchSaveTemplates(List<ChanceGoodsTemplate> templateList) {
        if (CollectionUtils.isEmpty(templateList)) {
            return;
        }

        LogUtils.info(log, "开始批量保存{}条模板数据", templateList.size());

        // 分批处理，避免单次操作数据量过大
        for (int i = 0; i < templateList.size(); i += BATCH_SIZE) {
            int endIndex = Math.min(i + BATCH_SIZE, templateList.size());
            List<ChanceGoodsTemplate> batchList = templateList.subList(i, endIndex);

            try {
                chanceGoodsTemplateService.saveBatch(batchList, BATCH_SIZE);
                LogUtils.info(log, "成功保存第{}批模板数据，共{}条", (i / BATCH_SIZE + 1), batchList.size());
            } catch (Exception e) {
                LogUtils.error(log, "保存第{}批模板数据失败", (i / BATCH_SIZE + 1), e);
                throw new RuntimeException("批量保存模板数据失败", e);
            }
        }
    }

    /**
     * 批量保存历史记录数据
     */
    private void batchSaveHistories(List<ChanceGoodsTemplateHistory> historyList) {
        if (CollectionUtils.isEmpty(historyList)) {
            return;
        }

        LogUtils.info(log, "开始批量保存{}条历史记录", historyList.size());

        // 分批处理，避免单次操作数据量过大
        for (int i = 0; i < historyList.size(); i += BATCH_SIZE) {
            int endIndex = Math.min(i + BATCH_SIZE, historyList.size());
            List<ChanceGoodsTemplateHistory> batchList = historyList.subList(i, endIndex);

            try {
                chanceGoodsTemplateHistoryService.saveBatch(batchList, BATCH_SIZE);
                LogUtils.info(log, "成功保存第{}批历史记录，共{}条", (i / BATCH_SIZE + 1), batchList.size());
            } catch (Exception e) {
                LogUtils.error(log, "保存第{}批历史记录失败", (i / BATCH_SIZE + 1), e);
                throw new RuntimeException("批量保存历史记录失败", e);
            }
        }
    }
}
