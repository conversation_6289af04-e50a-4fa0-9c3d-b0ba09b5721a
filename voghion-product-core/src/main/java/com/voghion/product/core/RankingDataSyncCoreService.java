package com.voghion.product.core;

import com.voghion.product.model.dto.RankingDataDTO;
import com.voghion.product.model.enums.RankingTypeEnums;

import java.util.Date;
import java.util.List;

/**
 * 榜单数据同步核心服务接口
 * 
 * <AUTHOR>
 * @since 2024-12-19
 */
public interface RankingDataSyncCoreService {

    /**
     * 同步周榜数据
     * 每天6点更新最近7天平台热门商品
     */
    void syncWeeklyRanking();

    /**
     * 同步月榜数据
     * 每周一6点更新最近30天平台热门商品
     */
    void syncMonthlyRanking();

    /**
     * 同步季度榜数据
     * 每个新季度6点更新最近4个季度的热销商品榜单
     */
    void syncQuarterlyRanking();

    /**
     * 同步指定类型的榜单数据
     * 
     * @param rankingType 榜单类型
     */
    void syncRankingByType(RankingTypeEnums rankingType);

    /**
     * 从ES索引获取榜单数据
     * 
     * @param rankingType 榜单类型
     * @return 榜单数据列表
     */
    List<RankingDataDTO> fetchRankingDataFromES(RankingTypeEnums rankingType);

    /**
     * 同步榜单数据到机会商品模板表
     * 
     * @param rankingDataList 榜单数据列表
     * @param rankingType 榜单类型
     */
    void syncRankingDataToTemplate(List<RankingDataDTO> rankingDataList, RankingTypeEnums rankingType);

    /**
     * 记录榜单变化历史
     * 
     * @param templateId 模板ID
     * @param goodsId 商品ID
     * @param rankingType 榜单类型
     * @param operationType 操作类型（进入/退出榜单）
     * @param rank 排名
     * @param hot 热度值
     */
    void recordRankingHistory(Long templateId, Long goodsId, RankingTypeEnums rankingType, 
                             Integer operationType, Integer rank, Integer hot);

    /**
     * 清理过期的榜单数据
     * 
     * @param rankingType 榜单类型
     * @param beforeDate 清理此日期之前的数据
     */
    void cleanExpiredRankingData(RankingTypeEnums rankingType, Date beforeDate);

    /**
     * 获取当前在榜商品列表
     * 
     * @param rankingType 榜单类型
     * @return 商品ID列表
     */
    List<Long> getCurrentRankingGoodsIds(RankingTypeEnums rankingType);

    /**
     * 批量更新商品模板信息
     * 
     * @param rankingDataList 榜单数据列表
     */
    void batchUpdateTemplateInfo(List<RankingDataDTO> rankingDataList);
}
