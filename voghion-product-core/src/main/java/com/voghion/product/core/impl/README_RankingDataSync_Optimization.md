# 榜单数据同步性能优化说明

## 优化背景

针对 `RankingDataSyncCoreServiceImpl` 类中的 `syncRankingByType` 方法进行性能优化，主要解决大数据量场景下的性能瓶颈问题。特别是周榜数据可能达到20万条的情况下，原有的单条处理方式会导致严重的性能问题。

## 原有问题分析

### 1. 单条数据库操作
- **问题**：`recordExitHistory` 和 `recordEnterHistory` 方法中使用循环单条插入历史记录
- **影响**：大量的数据库连接和事务开销，性能低下

### 2. 单条商品信息查询
- **问题**：在 `syncRankingDataToTemplate` 方法中逐个查询商品详情
- **影响**：N+1查询问题，数据库压力大

### 3. 缺乏批次处理机制
- **问题**：没有采用批量数据库操作
- **影响**：无法充分利用数据库的批处理性能优势

## 优化方案

### 1. 批量历史记录处理
- **新增方法**：`batchRecordExitHistory` 和 `batchRecordEnterHistory`
- **优化点**：
  - 批量查询模板信息
  - 批量查询商品信息
  - 批量构建历史记录对象
  - 批量保存历史记录

### 2. 批量商品信息查询
- **新增方法**：`batchQueryGoodsMap`
- **优化点**：
  - 一次性查询所有需要的商品信息
  - 构建商品ID到商品对象的映射
  - 避免N+1查询问题

### 3. 批量数据库操作
- **新增方法**：`batchSaveTemplates` 和 `batchSaveHistories`
- **优化点**：
  - 使用 MyBatis-Plus 的 `saveBatch` 方法
  - 设置合适的批次大小（500条）
  - 分批处理，避免单次操作数据量过大

### 4. 辅助方法优化
- **新增方法**：`buildChanceGoodsTemplate` 和 `buildRankingHistory`
- **优化点**：
  - 统一对象构建逻辑
  - 提高代码复用性和可维护性

## 技术实现细节

### 批次大小配置
```java
/**
 * 批处理大小 - 用于批量数据库操作
 */
private static final int BATCH_SIZE = 500;
```

### 批量查询商品信息
```java
/**
 * 批量查询商品信息并构建Map
 */
private Map<Long, Goods> batchQueryGoodsMap(List<Long> goodsIds) {
    if (CollectionUtils.isEmpty(goodsIds)) {
        return new HashMap<>();
    }
    List<Goods> goodsList = goodsService.listByIds(goodsIds);
    return goodsList.stream()
            .collect(Collectors.toMap(Goods::getId, goods -> goods));
}
```

### 批量保存优化
```java
/**
 * 批量保存历史记录数据
 */
private void batchSaveHistories(List<ChanceGoodsTemplateHistory> historyList) {
    // 分批处理，避免单次操作数据量过大
    for (int i = 0; i < historyList.size(); i += BATCH_SIZE) {
        int endIndex = Math.min(i + BATCH_SIZE, historyList.size());
        List<ChanceGoodsTemplateHistory> batchList = historyList.subList(i, endIndex);
        
        try {
            chanceGoodsTemplateHistoryService.saveBatch(batchList, BATCH_SIZE);
            LogUtils.info(log, "成功保存第{}批历史记录，共{}条", (i / BATCH_SIZE + 1), batchList.size());
        } catch (Exception e) {
            LogUtils.error(log, "保存第{}批历史记录失败", (i / BATCH_SIZE + 1), e);
            throw new RuntimeException("批量保存历史记录失败", e);
        }
    }
}
```

## 性能提升预期

### 1. 数据库操作次数减少
- **原来**：N次单条插入操作
- **优化后**：N/500次批量插入操作
- **提升**：数据库操作次数减少约99.8%

### 2. 查询性能优化
- **原来**：N次单条商品查询
- **优化后**：1次批量商品查询
- **提升**：查询次数减少约99.9%

### 3. 事务开销减少
- **原来**：每条记录一个事务
- **优化后**：每批次一个事务
- **提升**：事务开销减少约99.8%

## 兼容性说明

1. **接口兼容**：保持原有公共接口不变
2. **功能兼容**：保持原有业务逻辑完全一致
3. **数据兼容**：生成的数据结构与原来完全相同

## 监控和日志

优化后的代码增加了详细的批处理日志：
- 批处理开始和结束日志
- 每批次处理结果日志
- 异常处理和错误日志

## 使用建议

1. **监控性能**：部署后密切监控数据库性能指标
2. **调整批次大小**：根据实际情况可调整 `BATCH_SIZE` 常量
3. **错误处理**：关注批处理过程中的异常日志
4. **数据验证**：验证优化后的数据完整性和正确性

## 后续优化方向

1. **异步处理**：考虑将历史记录保存改为异步处理
2. **缓存优化**：对频繁查询的商品信息进行缓存
3. **分片处理**：对超大数据量进行分片并行处理
4. **监控告警**：添加性能监控和异常告警机制
