package com.voghion.product.core.impl;

import com.voghion.product.core.ChanceGoodsSyncCoreService;
import com.voghion.product.service.impl.AbstractCommonServiceImpl;
import org.opensearch.client.RestHighLevelClient;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;

@Component
public class ChanceGoodsSyncCoreServiceImpl extends AbstractCommonServiceImpl implements ChanceGoodsSyncCoreService {

    @Resource
    RestHighLevelClient restHighLevelClient;

    @Override
    public void syncChanceTemplate(Date date) {
        // 周榜为最近7天平台热门商品，每天六点更新；月榜为最近30天平台热门商品，每周一六点更新；另外会展示最近4个季度的热门榜，每个新季度六点更新。

        //syncChanceTemplateLast7Days();

        //syncChanceTemplateLast7Days();
    }
}
