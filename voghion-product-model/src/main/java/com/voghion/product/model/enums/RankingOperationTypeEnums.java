package com.voghion.product.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 榜单操作类型枚举
 * 
 * <AUTHOR>
 * @since 2024-12-19
 */
@Getter
@AllArgsConstructor
public enum RankingOperationTypeEnums {

    /**
     * 进入榜单
     */
    ENTER(1, "进入榜单"),

    /**
     * 退出榜单
     */
    EXIT(2, "退出榜单");

    private final Integer code;
    private final String description;

    /**
     * 根据代码获取枚举
     */
    public static RankingOperationTypeEnums getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (RankingOperationTypeEnums type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}
