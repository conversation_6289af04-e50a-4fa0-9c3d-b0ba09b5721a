package com.voghion.product.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Data
@Accessors(chain = true)
@ApiModel(value = "待处理事项")
public class ShopSummary4ShopIndexDTO {
    /**
     * 店铺id
     */
    @ApiModelProperty(value = "店铺id")
    private Long shopId;

    /**
     * 店铺名称
     */
    @ApiModelProperty(value = "店铺名称")
    private String shopName;

    /**
     * 店铺层级
     */
    @ApiModelProperty(value = "店铺层级")
    private String shopLevel = "底部卖家";

    /**
     * 店铺活跃度
     */
    @ApiModelProperty(value = "店铺活跃度")
    private String shopActive = "沉默";

    /**
     * 近30天gmv
     */
    @ApiModelProperty(value = "近30天gmv")
    private String nearly30GMV = "0.00€";

    /**
     * 店铺星级
     */
    @ApiModelProperty(value = "店铺星级")
    private Integer shopStar = 0;

    /**
     * 店铺综合星级统计时间段
     */
    @ApiModelProperty(value = "店铺综合星级统计时间段")
    private String shopStarSummaryTime = LocalDateTime.now().plusDays(-30).format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) + " ~ " + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

    /**
     * 近30天质量原因退款率
     */
    @ApiModelProperty(value = "近30天质量原因退款率")
    private Float qualityRefundRate30 ;

    /**
     * 近30天成交不卖率
     */
    @ApiModelProperty(value = "近30天成交不卖率")
    private Float dealundeliveryRefundRate30;

    /**
     * 近90的评价均分
     */
    @ApiModelProperty(value = "近90的评价均分")
    private BigDecimal commentAverScore90 = BigDecimal.ZERO;

    /**
     * 店铺经营更新时间
     */
    @ApiModelProperty(value = "店铺经营更新时间")
   private String shopUpdateTime = LocalDateTime.now().plusHours(-1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) + "23:59:59";

    /**
     * 店铺gmv
     */
    @ApiModelProperty(value = "店铺gmv")
   private BigDecimal shopGmv = BigDecimal.ZERO;

    /**
     * 店铺订单数
     */
    @ApiModelProperty(value = "店铺订单数")
   private Integer shopOrderNum = 0;

    /**
     * 动销商品数
     */
    @ApiModelProperty(value = "动销商品数")
   private Integer startSoldGoodsNum = 0;

    /**
     * 复购率
     */
    @ApiModelProperty(value = "复购率")
   private String repurchaseRate = "0.00%";

    /**
     * 转化率
     */
    @ApiModelProperty(value = "转化率")
   private String conversionRate = "0.00%";

    /**
     * 物流花费
     */
    @ApiModelProperty(value = "物流花费")
   private BigDecimal logisticsExpense = BigDecimal.ZERO;

    /**
     * 是否精选店铺 0 - 不是 1 - 是
     */
    @ApiModelProperty(value = "是否精选店铺")
    private Integer isSelected;

    /**
     * gmv时间线
     */
    @ApiModelProperty(value = "gmv时间线")
   private List<GmvTimeLine> gmvTimeLine;

    @ApiModelProperty(value = "商品曝光数量")
    private Long exposureGoodsCount = 0L;

    //近180天差评率
    @ApiModelProperty(value = "近180天差评率")
    private Float badCommentRate;


    @ApiModelProperty(value = "质量原因退款数-分子")
    private Integer appealApplyQualityCntM;

    @ApiModelProperty(value = "质量原因退款数-分母")
    private Integer appealApplyQualityCntD;


    @ApiModelProperty(value = "成交退款数-分子")
    private Integer dealUndeliveryRefundCntM;

    @ApiModelProperty(value = "成交退款数-分母")
    private Integer dealUndeliveryRefundCntD;


    @ApiModelProperty(value = "成交数")
    private Integer payCnt30;

    @ApiModelProperty(value = "评论数")
    private Integer commentCnt;

    @ApiModelProperty(value = "差评数")
    private Integer badCommentCnt;

    @ApiModelProperty(value = "30天动销率")
    private Float  dynamicSalesRate;

    @ApiModelProperty(value = "近30天支付转化率")
    private Float payRate;

    @ApiModelProperty(value = "近30天复购率")
    private Float payTwiceRate;

    /**
     * 限流/禁售，字段为空不展示
     */
    @ApiModelProperty(value = "限流/禁售，字段为空不展示")
    private String banSaleOrLimit;


    @Data
    @Accessors(chain = true)
    public static class GmvTimeLine{
        private String payDay;
        private BigDecimal gmv;
    }

}


