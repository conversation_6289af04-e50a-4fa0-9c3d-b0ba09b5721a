package com.voghion.product.model.enums;

public enum UpdateGoodsImageEnums {
    MAIN_IMAGE(1,"`product-ua`","goods", "main_image"),
    SKU_IMAGE(2,"`product-ua`","goods_item", "sku_image"),
    GOODS_IMAGE(3,"`product-ua`","goods_image", "url"),
    PROPERTY_IMAGE(4,"`product-ua`","property_img_detail", "img_url"),
    SIZE_IMAGE(5,"`product-ua`","goods_detail","size_image"),
    DETAIL_IMAGE(6,"`product-ua`","goods_ext_detail_img","img_url"),
    REAL_SHOT_IMAGE(7,"`product-ua`","goods_real_shot_img","img_url"),
    MANUAL(8,"`product-ua`","goods_manual","url"),
    ;
    private Integer goodsType;
    private String library;
    private String table;
    private String field;

    private UpdateGoodsImageEnums(Integer goodsType, String library, String table, String field) {
        this.goodsType = goodsType;
        this.library = library;
        this.table = table;
        this.field = field;
    }

    public String getTable() {
        return this.table;
    }

    public String getField() {
        return this.field;
    }

    public Integer getGoodsType() {
        return this.goodsType;
    }

    public String getLibrary(){
        return this.library;
    }
}
