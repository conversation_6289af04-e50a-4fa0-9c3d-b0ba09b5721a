package com.voghion.product.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description 每日审核量配置
 * <AUTHOR>
 * @Date 2025/4/18
 **/
@Data
@ApiModel(value = "每日审核量配置")
public class BlackSampleAuditCountConfigDTO {

    @ApiModelProperty(value = "类目ID")
    private Long categoryId;

    @ApiModelProperty(value = "商品数量")
    private Integer goodsCount;
}
