package com.voghion.product.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


@Data
public class ManualRefreshGoodsCountryDTO implements Serializable {


    private static final long serialVersionUID = 4121749775632142000L;

    private List<Long> goodsIds;

    private Integer logisticsProperty;

    private Boolean isCover = false;

    @ApiModelProperty(value = "操作(A新增D删除R替换)")
    private String operate;

    private List<String> countryCodeList;

    private Long beginGoodsId;

    private boolean needTonDun = true;
}
