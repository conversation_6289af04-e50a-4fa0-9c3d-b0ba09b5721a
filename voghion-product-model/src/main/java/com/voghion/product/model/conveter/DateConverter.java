package com.voghion.product.model.conveter;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.converters.WriteConverterContext;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.colorlight.base.utils.DateUtil;

import java.util.Date;

/**
 * ExcelLocalDateTimeConverter
 *
 * <AUTHOR>
 * @date 2022/4/26
 */
public class DateConverter implements Converter<Date> {
    private static final String DEFAULT_PATTERN = "yyyy-MM-dd HH:mm:ss";

    @Override
    public Class<Date> supportJavaTypeKey() {
        return Date.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public Date convertToJavaData(ReadCellData<?> cellData, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) {
        return DateUtil.parseDate(cellData.getStringValue(), DEFAULT_PATTERN);
    }

    @Override
    public WriteCellData<?> convertToExcelData(WriteConverterContext<Date> context) {
        return new WriteCellData<>(DateUtil.format(context.getValue(), DEFAULT_PATTERN));
    }
}
