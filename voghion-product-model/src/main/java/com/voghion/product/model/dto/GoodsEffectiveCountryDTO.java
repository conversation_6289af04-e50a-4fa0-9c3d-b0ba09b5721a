package com.voghion.product.model.dto;

import lombok.Data;

import java.io.Serializable;

@Data
public class GoodsEffectiveCountryDTO implements Serializable {

    /**
     * 商品id（新增商品id为null/更新取db）
     */
    private Long goodsId;
    /**
     * 商品shop_id(新增商品取输入/更新取db)
     */
    private Long shopId;
    /**
     * 商品物流属性(新增商品取输入/更新取db)
     */
    private Integer logisticsProperty;
    /**
     * 输入的国家(待校验的国家列表)
     */
    private String country;

    /**
     * 冗余类目Id,一个商品只对应一个后台类目Id
     */
    private Long categoryId;



}
