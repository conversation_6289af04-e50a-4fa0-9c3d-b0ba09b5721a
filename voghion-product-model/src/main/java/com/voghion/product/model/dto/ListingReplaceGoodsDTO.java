package com.voghion.product.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel
public class ListingReplaceGoodsDTO implements Serializable {
    private static final long serialVersionUID = -5040883427391397347L;

    @ApiModelProperty("原始商品id")
    private Long orgGoodsId;

    @ApiModelProperty("原始skuid")
    private Long orgSkuId;

    @ApiModelProperty("listing转换几率")
    private float replaceRate;

    @ApiModelProperty("原始listingId")
    private Long orgListingId;

    private List<ReplaceGoodsInfoDTO> replaceGoodsInfoDTOList;
}
