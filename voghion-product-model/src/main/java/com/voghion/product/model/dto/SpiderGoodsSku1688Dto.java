package com.voghion.product.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class SpiderGoodsSku1688Dto implements Serializable {
    private static final long serialVersionUID = 6091970767266831512L;

    private String title;
    private BigDecimal price;
    private Long stock;
    private Long saleQuantity;
    private String skuId;
    private String specId;


    private List<SaleProperty> saleProperties;
    private List<SkuPicture> skuPictures;

    @Data
    public static class SaleProperty {
        private String name;
        private String value;
    }

    @Data
    public static class SkuPicture {
        private String name;
        private String fileUrl;
    }


}
