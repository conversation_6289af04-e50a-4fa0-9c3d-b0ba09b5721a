package com.voghion.product.model.enums;

import com.google.common.collect.Lists;
import org.apache.commons.lang.StringUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @description:
 * @date: 2022/11/24 下午5:23
 * @author: jashley
 */
public enum GoodsLockLabelTypEnums {
    LAUNCH(1, "投放"),
    TEST(2, "测款"),
    HOT(3, "热卖"),
    FLASH_DEAL(4, "flashDeal"),
    SELECTION(5, "运营选品"),
    FULL_DECREASE(6, "满减大促"),
    SEVEN_DAY_DELIVERY(7, "七日达"),

    PICKED_SHOP(9, "精选店铺"),
    CHANCE_GOODS(8, "机会商品"),
    LISTING_GOODS(10, "listing商品"),
    PRICE(11, "价格锁")
    ;

    private final Integer code;

    private final String desc;

    GoodsLockLabelTypEnums(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static String getNameByCodes(String label) {
        if (StringUtils.isBlank(label)) {
            return "";
        }
        List<String> list = Lists.newArrayList();
        Arrays.stream(label.split(",")).forEach(s -> {
            for (GoodsLockLabelTypEnums value : GoodsLockLabelTypEnums.values()) {
                if (s.equals(value.getCode().toString())) {
                    list.add(value.getDesc());
                }
            }
        });
        return StringUtils.join(list, "、");
    }

    /**
     * 商品修改历史特殊标签(专用)
     */
    public static List<String> listNamesByCodes(String label) {
        if (StringUtils.isBlank(label)) {
            return Lists.newArrayList();
        }
        List<String> list = Lists.newArrayList();
        Arrays.stream(label.split(",")).forEach(s -> {
            if (FLASH_DEAL.getCode().toString().equals(s)) {
                list.add(FLASH_DEAL.getDesc());
            }
            if (SELECTION.getCode().toString().equals(s)) {
                list.add(SELECTION.getDesc());
            }
            if (FULL_DECREASE.getCode().toString().equals(s)) {
                list.add(FULL_DECREASE.getDesc());
            }
        });
        return list;
    }

    public static List<Integer> getAllLockLabels() {
        return Arrays.stream(GoodsLockLabelTypEnums.values())
                .map(GoodsLockLabelTypEnums::getCode)
                .collect(Collectors.toList());
    }

    public static List<Integer> getAllOperationLockLabels() {
        return Arrays.stream(GoodsLockLabelTypEnums.values())
                .map(GoodsLockLabelTypEnums::getCode)
                .filter(integer -> integer != 1)
                .collect(Collectors.toList());
    }

    public static String getMsgByCode(Integer code) {
        for (GoodsLockLabelTypEnums enums : GoodsLockLabelTypEnums.values()) {
            if (enums.code.equals(code)) {
                return enums.getDesc();
            }
        }
        return "";
    }
}
