package com.voghion.product.model.dto;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.voghion.product.model.po.GoodsRealShotImg;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


@Data
public class ListingDimensionDataDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long id;
    // 对应标题字符数 caption_cnt -> es:name
    private String name;
    // 对应商详字符数 des_cnt  -> es:goodsDetailModel.description
    private String description;
    // 对应模板属性个数 attributes_filled_cnt  -> es:propertyGoodsInfoModels
    private JSONArray templatePropertyInfos;
    // 对应自定义属性个数 custom_attributes_cnt  -> db:goods_extra.property_json
    private JSONArray customPropertyInfos;
    // 对应图片个数 pic_cnt  -> es:goodsImageList  type 1
    private List<JSONObject> goodsImageList;
    // 对应商详图片数 des_pic_cnt  -> es:goodsExtDetailImgList
    private JSONArray goodsExtDetailImgList;
    // 对应视频个数  video_cnt  -> es:goodsImageList  type 2
    private List<JSONObject>  goodsVideoList;
    // 实拍图数量  real_pic_cnt
    private List<GoodsRealShotImg> goodsRealShotImgList;
}
