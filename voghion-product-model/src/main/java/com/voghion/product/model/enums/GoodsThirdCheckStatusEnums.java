package com.voghion.product.model.enums;

public enum GoodsThirdCheckStatusEnums {

    WAIT_AUDIT(0, "待审核"),
    AUDITING(1, "审核中"),
    AUDIT_OK(2, "审核通过"),
    AUDIT_FAIL(3, "审核驳回"),
    AUDIT_ERROR(4, "审核异常")
    ;

    private final Integer code;
    private final String desc;

    GoodsThirdCheckStatusEnums(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }

    public static String getDescByCode(Integer code) {
        for (GoodsThirdCheckStatusEnums value : GoodsThirdCheckStatusEnums.values()) {
            if (value.getCode().equals(code)) {
                return value.getDesc();
            }
        }
        return null;
    }
}
