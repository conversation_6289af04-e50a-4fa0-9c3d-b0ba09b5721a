package com.voghion.product.model.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.voghion.product.model.vo.PropertyDetailInfoVO;
import com.voghion.product.model.vo.PropertyGoodsInfoVO;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <p>
 * 商品规范表
 * </p>
 *
 * <AUTHOR> @since 2022-09-16
 */
@Data
@NoArgsConstructor
public class PropertyGoodsInfoDTO  {


    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 商品id
     */
    private Long goodsId;

    /**
     * 材质中文名
     */
    private String propertyCnName;

    /**
     * 材质英文名
     */
    private String propertyEnName;

    /**
     * 填写类型 1:单选 2:二选一 3:复选 4:自由填写(仅数字) 5:自由填写(仅英文) 6:自由填写(数字+英文)
     */
    private Integer type;

    /**
     * 是否必填 0:非必填 1:必填
     */
    private Integer required;

    /**
     * 最后修改时间
     */
    private String inputValue;

    /**
     * 模板明细表id
     */
    private Long propertyDetailId;

    /**
     * 单位
     */
    private String unit;

    /**
     * 状态 0生效 1失效
     */
    private Integer status;

    private Long standardId;

    /**
     * 属性详情
     * */
    private List<PropertyGoodsInfoVO> propertyGoodsInfoVOS;

    /**
     * 属性模板详情
     * */
    private List<PropertyDetailInfoVO> propertyDetailInfoVOList;

    public PropertyGoodsInfoDTO(Long goodsId) {
        this.goodsId = goodsId;
    }
}
