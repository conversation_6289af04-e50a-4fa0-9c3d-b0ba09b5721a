package com.voghion.product.model.constant;

import com.voghion.product.model.enums.GoodsIsShowEnums;

/**
 * @Description: 检查异常常量
 * @author: shuaibo
 * @Date: 2019/6/10 21:47
 */
public class CheckConstant {
    public static final String GOODS_EXIST = "商品已经存";
    public static final String GOODS_NO_EXIST = "商品不存在";
    public static final String CATEGORY_IS_NULL = "类目为空";
    public static final String INSERT_BRAND_FAIL = "增加品牌失败";
    public static final String INSERT_CATEGORY_FAIL = "增加类目失败";
    public static final String INSERT_GOODS_FAIL = "增加goods失败";
    public static final String UPDATE_GOODS_FAIL = "更新goods失败";
    public static final String PARAM_ERROR = "请求参数有误";
    public static final String NOT_PARAM_DATA = "没有对应的数据";

    //是否删除 0表示未删除 1表示已删除
    public static final Integer GOODS_NOT_DELETE = 0;
    public static final Integer GOODS_HAVE_DELETE = 1;

    //是否已翻译 0表示未翻译，1表示已翻译
    public static Integer UNTRANSLATED = 0;
    public static Integer TRANSLATED = 1;

    //状态 1表示正常 99表示禁用 2 商品限流标识 3 商品限流
    public static final Integer GOODS_NORMAL_STATUS = 1;
    public static final Integer GOODS_FORBIDDEN_STATUS = 99;
    public static final Integer GOODS_Limiting_STATUS = 2;
    public static final Integer GOODS_BEGIN_LIMITING_STATUS = 3;

    //商品类型 1表示单品 2表示组合商品
    public static final Integer GOODS_PRODUCT_TYPE = 1;
    public static final Integer GOODS_BUNDLES_TYPE = 2;

    //是否上架 0-下架  1-上架 2-待审核 5表示审核中
    public static final String GOODS_SHOW = "1";
    public static final String GOODS_NOT_SHOW = "0";
    public static final String GOODS_AUDIT_STATUS = "5";

    public static final String GOODS_WAIT_AUDIT = String.valueOf(GoodsIsShowEnums.WAIT_AUDIT.getType());

    //关联词个数限制
    public static final int RELATEDWORDMAX = 10;
    public static final int KEYWORDMAX = 1;

    //作废相似品
    public static final int DISABLE_SIMILAR_IMAGE_GOODS = -1;

    //相似品设为采购链接
    public static final int SIMILAR_IMAGE_GOODS_PURCHASE_FLAG = 1;



}
