package com.voghion.product.model.enums;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * author archer
 * createOn 2021/5/9 0:02
 */
public enum PriceRuleEnum {
    /*LEVEL0(0.0,0.2,	13.0),
    <PERSON>E<PERSON>L1(0.2,0.5,	8.0),
    <PERSON>E<PERSON>L2(0.5,1.5,	6.0),
    <PERSON>EVEL3(1.5,2.5,	5.0),
    <PERSON>EVEL4(2.5,20.0,	4.0),
    LEVEL5(20.0,50.0,	3.0),
    LEVEL6(50.0,80.0,	2.0),
    <PERSON>EVEL7(80.0,2000000.0,	1.5),*/
    LEVEL0(0.0,0.25,	7.5),
    LEVEL1(0.25,0.65,	6.875),
    LEVEL2(0.65,1.25,	5.0),
    LEVEL3(1.25,2.5,	3.75),
    LEVEL4(2.5,4.5,	3.125),
    LEVEL5(4.5,7.0,	3.0),
    <PERSON>EVEL6(7.0,10.5,	2.875),
    <PERSON>EVEL7(10.5,2000000.0,	2.625),
    ;


    private BigDecimal beginPrice;

    private BigDecimal endPrice;

    private BigDecimal rule;

     PriceRuleEnum(Double beginPrice,Double endPrice,Double rule){
        this.beginPrice=new BigDecimal(beginPrice).setScale(2, RoundingMode.HALF_UP);
        this.endPrice=new BigDecimal(endPrice).setScale(2, RoundingMode.HALF_UP);
        this.rule=new BigDecimal(rule).setScale(2, RoundingMode.HALF_UP);
    }


    public BigDecimal getBeginPrice() {
        return beginPrice;
    }

    public BigDecimal getEndPrice() {
        return endPrice;
    }

    public BigDecimal getRule() {
        return rule;
    }
}
