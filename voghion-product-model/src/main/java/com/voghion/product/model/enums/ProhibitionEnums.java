package com.voghion.product.model.enums;

public enum ProhibitionEnums {
    GOODS_NOT_RIGHT(1,"货不对板"),
    BRANDINFR_INGEMENT(2,"品牌侵权"),
    PLATFORM_BANNED_GOODS(3,"平台禁售商品"),
    SUBSTANDARD_DELIVERY(4,"发货不达标"),
    OTHER(5,"其他"),
    ;
    private Integer code;
    private String desc;

    ProhibitionEnums(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
