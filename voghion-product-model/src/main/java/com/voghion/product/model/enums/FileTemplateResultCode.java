package com.voghion.product.model.enums;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.colorlight.base.model.BaseResultCode;


public enum FileTemplateResultCode implements BaseResultCode{

    PARAMS_IS_NULL("201000000","参数异常！！！","参数异常！！！"),
    PARAMS_FILE_NULL_ERROR("201000001", "参数 file 异常！！！", "参数 file 异常！！！"),
    FILE_TEMPLATE_EXIST("201000002", "文件名已经存在！！！", "文件名已经存在！！！"),
    FILE_TEMPLATE_UPLOAD_ERROR("201000003", "文件上传失败！！！", "文件上传失败！！！"),
    FILE_TEMPLATE_UPDATE_ERROR("201000003", "文件更新失败！！！", "文件更新失败！！！"),

    PARAMS_ID_ERROR("201000004", "参数：id 异常！！！", "参数：id 异常！！!"),
    FETCH_FILE_NAME_ERROR("201000005", "获取文件名异常！！", "获取文件名异常！！"),
    UPDATE_CONTENT_IS_EMPTY("201000006", "更新内容为空！！", "更新内容为空！！"),

    ;

    /**
     * 结果码
     */
    private final String code;
    /**
     * 返回消息
     */
    private String msg;
    /**
     * 描述
     */
    private final String detail;

     FileTemplateResultCode(String code, String msg, String detail) {
        this.code = code;
        this.msg = msg;
        this.detail = detail;
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getDetail() {
        return detail;
    }

    @Override
    public String getDetail(String[] arg0) {
        return null;
    }

    @Override
    public String getMsg() {
        return msg;
    }

    private void setMsg(String msg) {
         this.msg = msg;
    }

    @Override
    public String getName() {
        return name();
    }

    public static FileTemplateResultCode getEnumByCode(String code) {
        for(FileTemplateResultCode p : FileTemplateResultCode.values()) {
            if(p.getCode().equalsIgnoreCase(code)) {
                return p;
            }
        }
        return null;
    }
    

    @Override
    public String toString() {
        JSONObject object = new JSONObject();
        object.put("code", code);
        object.put("msg", msg);
        return JSON.toJSONString(object);
    }

}
