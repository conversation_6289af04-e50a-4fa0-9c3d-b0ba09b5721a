package com.voghion.product.model.enums;

import com.colorlight.base.utils.CheckUtils;

public enum AuditTaskTypeEnum {
    COMPLIANCE_LABEL_ALL(0, "complianceLabel", "合规打标页面的所有类型"),
    COMPLIANCE_LABEL(1, "complianceLabel", "合规打标"),
    LAUNCH_AUTOMATION(2, "launchAutomation", "投放自动化"),
    MERCHANTS_APPEAL(3, "merchantsAppeal", "商家申诉"),
    SECOND_CHECK(4, "secondCheck", "二次审核"),
    USER_REPORT(5, "userReport", "用户举报"),
    SELF_RECHECK(6, "selfRecheck", "自营复审"),
    BLACK_SAMPLE_MARK(10, "blackSampleMark", "黑样本标注"),
    ;

    private Integer code;
    private String name;
    private String desc;

    AuditTaskTypeEnum(Integer code, String name, String desc) {
        this.code = code;
        this.name = name;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public String getDesc() {
        return desc;
    }

    public static String getTaskTypeNameByCode(Integer code) {
        for (AuditTaskTypeEnum typeEnum : AuditTaskTypeEnum.values()) {
            if (typeEnum.getCode().equals(code)) {
                return typeEnum.getName();
            }
        }
        CheckUtils.check(true, ProductResultCode.AUDIT_TASK_TYPE_INVALID);
        return null;
    }

    public static String getTaskDescByCode(Integer code) {
        for (AuditTaskTypeEnum typeEnum : AuditTaskTypeEnum.values()) {
            if (typeEnum.getCode().equals(code)) {
                return typeEnum.getDesc();
            }
        }
        CheckUtils.check(true, ProductResultCode.AUDIT_TASK_TYPE_INVALID);
        return null;
    }
}