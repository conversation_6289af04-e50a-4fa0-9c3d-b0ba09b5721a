package com.voghion.product.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * 店铺禁售：封禁/限流类型
 */
@Getter
@AllArgsConstructor
public enum ShopsProhibitionTypeEnum {

    DAYS(1, "天数"),
    PERMANENT(2, "永久")

    ;


    private final Integer type;
    private final String desc;

    public static ShopsProhibitionTypeEnum getByType(Integer type) {
        return Arrays.stream(ShopsProhibitionTypeEnum.values())
                .filter(typeEnum -> Objects.equals(typeEnum.getType(), type))
                .findFirst().orElse(null);
    }

}
