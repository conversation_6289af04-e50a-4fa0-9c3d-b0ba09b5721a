package com.voghion.product.model.enums;

import org.apache.commons.collections4.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

public enum ManageTypeEnum {
    //
    TRADERS(1, "一般贸易商"),
    BRAND(2, "品牌授权商"),
    FACTORY(3, "普通工厂"),
    TOP500_FACTORY(4, "世界500强代工厂"),
    ;

    private Integer code;
    private String name;

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    ManageTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getNameByCode(Integer code) {
        if (code != null) {
            for (ManageTypeEnum value : ManageTypeEnum.values()) {
                if (value.getCode().equals(code)) {
                    return value.getName();
                }
            }
            if (code == 10) {
                return "其他";
            }
        }
        return TRADERS.getName();
    }

    public static List<String> getNameListByCode(List<Integer> codes) {
        if (CollectionUtils.isEmpty(codes)) {
            return Collections.emptyList();
        }

        return codes.stream()
                .map(ManageTypeEnum::getNameByCode)
                .collect(Collectors.toList());
    }
}
