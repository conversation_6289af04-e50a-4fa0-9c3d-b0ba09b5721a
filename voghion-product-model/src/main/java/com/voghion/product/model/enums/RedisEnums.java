package com.voghion.product.model.enums;

/**
 * <AUTHOR>
 * @time 2019/6/25 20:08
 * @describe
 */
public enum RedisEnums {

    REDIS_QUERY_GOODS_KEY("QUERY_GOODS_", "查询商品信息"),
    REDIS_KFBUY_ATEGORY_LEAFCATES("REDIS_KFBUY_ATEGORY_LEAFCATES_V2", "类目下的叶子类目"),
    REDIS_CATEGORY_WHOLE_LIST("REDIS_CATEGORY_WHOLE_LIST", "所有的类目"),
    REDIS_KFBUY_CATEGORY_WHOLE_TREE("REDIS_KFBUY_CATEGORY_WHOLE_TREE", "完整的类目树"),
    REDIS_KFBUY_CATEGORY_GOODS_TREE("REDIS_KFBUY_CATEGORY_GOODS_TREE", "完整的商品类目树"),
    REDIS_BRAND_CACHE("REDIS_BRAND_CACHE","品牌"),
    REDIS_PROPERTY_CACHE("REDIS_PROPERTY_CACHE","属性"),
    REDIS_PROPERTY_VALUE_CACHE("REDIS_PROPERTY_VALUE_CACHE","属性值"),
    REDIS_CATEGORY_CACHE("REDIS_CATEGORY_CACHE","类目"),
    REDIS_QUERY_GOODS_SKU("REDIS_QUERY_GOODS_SKU","查询商品sku信息"),
    REDIS_EDIT_GOODS_CATEGORY_TAG("REDIS_EDIT_GOODS_CATEGORY_TAG","标记正在执行任务"),
    REDIS_EDIT_GOODS_REPLACE_NAME("REDIS_EDIT_GOODS_REPLACE_NAME","标记正在执行任务"),
    REDIS_EDIT_GOODS_UPDATE_SIZE_IMAGE("REDIS_EDIT_GOODS_UPDATE_SIZE_IMAGE","标记正在执行任务"),

    ;
    private String code;
    private String desc;

    private RedisEnums(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
