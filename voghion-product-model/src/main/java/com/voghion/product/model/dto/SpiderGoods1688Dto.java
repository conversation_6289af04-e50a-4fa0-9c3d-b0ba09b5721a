package com.voghion.product.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class SpiderGoods1688Dto implements Serializable {
    private static final long serialVersionUID = 6091970767266831512L;


    private Long goodsMoveId;

    private String goodsUrl;

    private String name;

    private String categoryId;

    private String categoryName;

    private String breadCrumb;

    private String url;

    private Long productId;

    private Integer batchNum;

    private Long sold;

    private String afterService;

    private Long stock;

    private String description;

    private String scoreRate;

    private String score;

    private String commentNum;

    private String customerReviews;

    /**
     * 卖家会员账号
     */
    private String sellerMemberId;

    private String sellerLoginId;

    /**
     * 卖家用户ID
     */
    private Long sellerUserId;

    /**
     * 卖家店铺名称
     */
    private String companyName;


    private List<SpiderImage> imageList;

    private List<SpiderProperty> properties;

    private List<SpiderGoodsSku1688Dto> skuList;

    private List<SpiderGoodsSkuProp1688Dto> skuProps;


    @Data
    public static class SpiderProperty {
        private String name;
        private String value;
        private Integer type;
        private String imageUrl;
    }

    @Data
    public static class SpiderImage {
        private Integer fileType;
        private String fileUrl;
    }
}
