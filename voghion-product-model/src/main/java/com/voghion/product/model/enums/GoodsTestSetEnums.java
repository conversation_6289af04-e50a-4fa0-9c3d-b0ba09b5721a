package com.voghion.product.model.enums;

public enum GoodsTestSetEnums {
    UNDER_TEST(1,"测试中"),
    TEST_COMPLETED(2,"测试完成"),
    ;

    /**
     * 状态
     */
    private Integer code;

    /**
     * 描述
     */
    private String desc;

    GoodsTestSetEnums(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static String getStatusDesc(Integer code){
        String messages = "";
        for(GoodsTestSetEnums opt:GoodsTestSetEnums.values()){
            if(code.equals(opt.getCode())){
                messages = opt.getDesc();
                break;
            }
        }
        return messages;
    }
}
