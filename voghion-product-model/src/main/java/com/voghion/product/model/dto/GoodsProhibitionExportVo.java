package com.voghion.product.model.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class GoodsProhibitionExportVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ExcelProperty(value ="商品id")
    private Long goodsId;

    @ExcelProperty(value ="商品名称")
    private String goodsName;

    @ExcelProperty(value ="商家id")
    private Long shopId;

    @ExcelProperty(value ="商家名称")
    private String shopName;

    @ExcelProperty(value ="状态")
    private String status;

    @ExcelProperty(value ="限流次数")
    private Integer limitingNumber;

    @ExcelProperty(value ="限流天数")
    private Integer limitingDays;

    @ExcelProperty(value ="封禁次数")
    private Integer number;

    @ExcelProperty(value ="禁售天数")
    private Integer prohibitionDays;

    @ExcelProperty(value ="原因")
    private String reason;

    @ExcelProperty(value ="创建人")
    private String createUser;

    @ExcelProperty(value ="创建时间")
    private String createTime;

    @ExcelProperty(value ="更新人")
    private String updateUser;

    @ExcelProperty(value ="更新时间")
    private String updateTime;








}
