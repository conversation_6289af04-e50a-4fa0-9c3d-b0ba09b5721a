package com.voghion.product.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class FilterLabelCategoryUpdateDTO {

    @ApiModelProperty(value = "目录id",required = true)
    @NotNull(message = "目录id不能为空")
    private Long id;

    @ApiModelProperty(value = "目录名称",required = true)
    @NotBlank(message = "目录名称不能为空")
    private String name;

}
