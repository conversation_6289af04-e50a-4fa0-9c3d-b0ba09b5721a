package com.voghion.product.model.conveter;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.colorlight.base.model.PageView;
import com.voghion.product.model.po.ShopsProhibition;
import com.voghion.product.model.vo.ShopBanSaleOrLimitingVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface ShopsProhibitionConverter {

    ShopsProhibitionConverter INSTANCE = Mappers.getMapper(ShopsProhibitionConverter.class);

    List<ShopBanSaleOrLimitingVO> convert(List<ShopsProhibition> shopsProhibitions);

    default void fillPageView(PageView<?> pageView, IPage<?> result) {
        pageView.setPageCount(result.getPages());
        pageView.setRowCount(result.getTotal());
        pageView.setPageNow((int) result.getCurrent());
        pageView.setPageSize((int) result.getSize());
    }

}
