package com.voghion.product.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/9 17:00
 */
@Data
public class ItemGoodsPropertyValueDto implements Serializable {

    @ApiModelProperty("商品规格关联表 id")
    private Long id;

    @ApiModelProperty("商品规格值名称(别名)")
    private String value;

    @ApiModelProperty("商品规格值排序")
    private Integer sort;

    @ApiModelProperty("商品规格值携带的图片(主规格必填，非主规格没有)")
    private List<ItemGoodsPropertyImgDto> imgList;

}
