package com.voghion.product.model.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class FavoritesGoodsModel {

    @ExcelProperty(value = "商品id", index = 0)
    private Long goodsId;

    /**
     * 活动名称
     */
    @ExcelProperty(value = "商品名称", index = 1)
    private String goodsName;

    /**
     * 活动商品主图
     */
    @ExcelProperty(value = "类目名称", index = 2)
    private String categoryName;

    /**
     * 商品id
     */
    @ExcelProperty(value = "商家名称", index = 3)
    private String shopName;

    /**
     * 品类id
     */
    @ExcelProperty(value = "最低价", index = 4)
    private BigDecimal minPrice;


    @ExcelProperty(value = "最高价", index = 5)
    private BigDecimal maxPrice;

    @ExcelProperty(value = "商品状态(0下架 1上架 3禁售 100,101,103商家禁售)", index = 6)
    private String isShow;

    @ExcelProperty(value = "近30天gmv", index = 7)
    private BigDecimal gmv;

    @ExcelProperty(value = "锁定标签信息(1投放 2测款 3热卖 4flashDeal 5运营选品 6满减大促 7七日达)", index = 8)
    private String lockLabelInfo;

    @ExcelProperty(value = "报名的活动信息(1flashdeal 2满减 3运营选品 4七日达)", index = 9)
    private String signActivityInfo;

    @ExcelProperty(value = "正在参与的活动信息(1flashdeal 2满减 3运营选品 4七日达)", index = 10)
    private String joinActivityInfo;

    @ExcelProperty(value = "商品收藏状态(0删除  1正常)", index = 11)
    private String favoriteStatus;

    @ExcelProperty(value = "备注", index = 12)
    private String remarks;

    @ExcelProperty(value = "添加人", index = 13)
    private String createUserName;

}
