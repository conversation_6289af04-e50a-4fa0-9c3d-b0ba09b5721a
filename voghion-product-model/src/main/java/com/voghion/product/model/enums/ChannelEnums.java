package com.voghion.product.model.enums;

/**
 * <AUTHOR>
 * @time 2019/6/26 15:20
 * @describe
 */
public enum ChannelEnums {
    SPIDER_ALIEXPRESS(1, "速卖通爬虫导入"),
    SPIDER_1688(2, "1688爬虫导入"),
    ALI_OPEN(3, "从1688开放平台导入"),
    SPIDER_NIHAO(4, "nihao爬虫导入"),
    DO_BA(5, "doba导入"),
    ALIEXPRESS_IMPORT_GOODS(6, "速卖通手动批量导入"),
    PING_DUO_DUO(8, "拼多多"),
    TAO_BAO(9, "淘宝"),
    XI_ZHI_YUE(10, "西之月"),

    EXCEL_IMPORT(11, "商家excel导入"),
    SCM_ADD_GOODS(12, "scm供应商商品同步新增"),
    ERP_ADD_GOODS(13, "erp新增商品"),
    COPY_GOODS(14, "复制商品"),

    GIGA(20, "大健云仓"),
    LECANG(21, "乐歌"),

    CREATE_LISTING_TEMPLATE(61, "创建listing商品模板"),
    CREATE_LISTING_FOLLOW_GOODS(62, "商家竞标listing并新增商品"),

    CREATE_CHANCE_GOODS_TEMPLATE(71, "创建机会商品模板"),
    CREATE_SAME_CHANCE_GOODS_FROM_TEMPLATE(72, "根据机会商品模板发布同款商品"),

    MANUAL(99, "voghion后台手动新增"),

    ;
    private Integer code;
    private String desc;

    ChannelEnums(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 转换成枚举类型
     * @param channel
     * @return
     */
    public static String valueOfByChannel(Integer channel){
        String messages = "";
        for(ChannelEnums opt:ChannelEnums.values()){
            if(channel.equals(opt.getCode())){
                messages = opt.getDesc();
                break;
            }
        }
        return messages;
    }
}
