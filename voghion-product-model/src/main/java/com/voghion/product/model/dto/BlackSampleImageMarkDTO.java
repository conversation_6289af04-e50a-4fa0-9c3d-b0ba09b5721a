package com.voghion.product.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Description 黑白样本图片标注数据
 * <AUTHOR>
 * @Date 2025/4/18
 **/
@Data
@ApiModel(value = "黑白样本图片标注数据")
public class BlackSampleImageMarkDTO {

    @ApiModelProperty(value = "任务ID")
    private Long taskId;

    @ApiModelProperty(value = "品牌ID")
    private Long brandId;

    @ApiModelProperty(value = "黑样本图片")
    private List<String> blackImages;

    @ApiModelProperty(value = "白样本图片")
    private List<String> whiteImages;
}
