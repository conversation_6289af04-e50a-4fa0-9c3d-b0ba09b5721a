package com.voghion.product.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel
public class BlackSampleTaskDistributeDTO implements Serializable {
    private static final long serialVersionUID = 8151938542867161812L;

    @ApiModelProperty("任务id")
    private List<Long> taskIds;

    @ApiModelProperty("审核人id")
    private Long auditUserId;
}
