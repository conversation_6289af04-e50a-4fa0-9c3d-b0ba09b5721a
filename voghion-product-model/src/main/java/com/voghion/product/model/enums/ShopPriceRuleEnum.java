package com.voghion.product.model.enums;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * author archer
 * createOn 2021/5/9 0:02
 */
public enum ShopPriceRuleEnum {

    LEVEL0(0.0,1.33,	1.5,3.0),
    LEVEL1(1.33,1.93,	1.6,2.6),
    LEVEL2(1.93,3.18,	1.75,3.27),
    LEVEL3(3.18,7.68,	1.85,4.68),
    LEVEL4(7.68,2000000.0,	1.9,5.2),
    ;


    private BigDecimal beginPrice;

    private BigDecimal endPrice;

    private BigDecimal platformRatio;

    private BigDecimal marketRatio;

     ShopPriceRuleEnum(Double beginPrice, Double endPrice, Double platformRatio,Double marketRatio ){
        this.beginPrice=new BigDecimal(beginPrice).setScale(2, RoundingMode.HALF_UP);
        this.endPrice=new BigDecimal(endPrice).setScale(2, RoundingMode.HALF_UP);
        this.platformRatio=new BigDecimal(platformRatio).setScale(2, RoundingMode.HALF_UP);
         this.marketRatio=new BigDecimal(marketRatio).setScale(2, RoundingMode.HALF_UP);
    }


    public BigDecimal getBeginPrice() {
        return beginPrice;
    }

    public BigDecimal getEndPrice() {
        return endPrice;
    }

    public BigDecimal getPlatformRatio() {
        return platformRatio;
    }

    public BigDecimal getMarketRatio() {return marketRatio;}
}
