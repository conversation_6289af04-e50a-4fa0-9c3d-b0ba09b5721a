package com.voghion.product.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Description 黑白样本图片更新
 * <AUTHOR>
 * @Date 2025/4/18
 **/
@Data
@ApiModel(value = "黑白样本图片更新")
public class BlackSampleImageUpdateDTO {

    @ApiModelProperty(value = "样本ID集合")
    private List<Long> ids;

    @ApiModelProperty(value = "黑白样本 0黑样本 1白样本")
    private Integer type;
}
