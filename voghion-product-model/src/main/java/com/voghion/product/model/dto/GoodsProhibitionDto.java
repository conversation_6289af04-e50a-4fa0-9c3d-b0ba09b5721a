package com.voghion.product.model.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.colorlight.base.utils.DateUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.voghion.product.model.po.Goods;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class GoodsProhibitionDto extends Model<GoodsProhibitionDto> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 商品id
     */
    private Long goodsId;

    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 店铺id
     */
    private Long shopId;
    /**
     * 封禁次数
     */
    private Integer number;
    /**
     * 状态 1表示解封 2 表示封禁中 3 限流中 4 解除限流
     */
    private String status;

    /**
     * 封禁类型 1 7天 2 永久
     */
    private String type;

    /**
     * 封禁理由枚举id
     */
    private Integer reasonType;

    /**
     * 封禁理由
     */
    private String reason;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = DateUtil.newFormat)
    private LocalDateTime createTime;

    @JsonFormat(pattern = DateUtil.newFormat)
    private LocalDateTime updateTime;

    private String createUser;

    private String updateUser;

    /**
     * 禁售天数
     */
    private Integer prohibitionDays;

    /**
     * 限流天数
     */
    private Integer limitingDays;

    /**
     * 限流次数
     */
    private Integer limitingNumber;

}
