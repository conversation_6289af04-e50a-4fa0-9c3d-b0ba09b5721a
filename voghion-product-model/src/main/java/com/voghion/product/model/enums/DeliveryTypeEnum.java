package com.voghion.product.model.enums;

import org.apache.commons.collections4.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

public enum DeliveryTypeEnum {
    //
    AGENT(0, "代发商家"),
    DIRECT(1, "直发商家"),
    HK_AGENT(2, "香港代发"),
    SELF_SUPPORT(3, "自营店铺"),
    SELF_SUPPORT_SCM(4, "scm自营店铺"),
    ;

    private Integer code;
    private String name;

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    DeliveryTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getNameByCode(Integer code) {
        if (code != null) {
            for (DeliveryTypeEnum value : DeliveryTypeEnum.values()) {
                if (value.getCode().equals(code)) {
                    return value.getName();
                }
            }
            if (code == 10) {
                return "其他";
            }
        }

        return AGENT.getName();
    }

    public static List<String> getNameListByCode(List<Integer> codes) {
        if (CollectionUtils.isEmpty(codes)) {
            return Collections.emptyList();
        }

        return codes.stream()
                .map(DeliveryTypeEnum::getNameByCode)
                .collect(Collectors.toList());
    }
}
