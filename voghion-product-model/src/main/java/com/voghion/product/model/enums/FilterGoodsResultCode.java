package com.voghion.product.model.enums;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.colorlight.base.model.BaseResultCode;


public enum FilterGoodsResultCode implements BaseResultCode{


    FILTER_LABEL_CATEGORY_NOT_EXIST("1000001", "拦截词标签类目不存在", "拦截词标签类目不存在"),
    PARENT_FILTER_LABEL_CATEGORY_NOT_EXIST("1000002", "拦截词标签父类目不存在", "拦截词标签父类目不存在"),
    FILTER_LABEL_CATEGORY_LEVEL_ERROR("1000003", "拦截词标签类目层级仅支持两级", "拦截词标签类目层级仅支持两级");

    /**
     * 结果码
     */
    private final String code;
    /**
     * 返回消息
     */
    private String msg;
    /**
     * 描述
     */
    private final String detail;

     FilterGoodsResultCode(String code, String msg, String detail) {
        this.code = code;
        this.msg = msg;
        this.detail = detail;
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getDetail() {
        return detail;
    }

    @Override
    public String getDetail(String[] arg0) {
        return null;
    }

    @Override
    public String getMsg() {
        return msg;
    }

    private void setMsg(String msg) {
         this.msg = msg;
    }

    @Override
    public String getName() {
        return name();
    }

    public static FilterGoodsResultCode getEnumByCode(String code) {
        for(FilterGoodsResultCode p : FilterGoodsResultCode.values()) {
            if(p.getCode().equalsIgnoreCase(code)) {
                return p;
            }
        }
        return null;
    }
    

    @Override
    public String toString() {
        JSONObject object = new JSONObject();
        object.put("code", code);
        object.put("msg", msg);
        return JSON.toJSONString(object);
    }

}
