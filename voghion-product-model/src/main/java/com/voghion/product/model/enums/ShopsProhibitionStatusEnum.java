package com.voghion.product.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * 店铺禁售：封禁/限流类型
 */
@Getter
@AllArgsConstructor
public enum ShopsProhibitionStatusEnum {

    UN_DISABLE(1, "解除禁用"),
    DISABLE(99, "禁用中"),
    UN_LIMITING(10, "已解限流"),
    LIMITING(20, "限流中"),

    ;


    private final Integer type;
    private final String desc;

    public static ShopsProhibitionStatusEnum getByType(Integer type) {
        return Arrays.stream(ShopsProhibitionStatusEnum.values())
                .filter(typeEnum -> Objects.equals(typeEnum.getType(), type))
                .findFirst().orElse(null);
    }
}
