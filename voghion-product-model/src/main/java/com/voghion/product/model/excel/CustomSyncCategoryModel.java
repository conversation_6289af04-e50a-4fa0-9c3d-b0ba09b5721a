package com.voghion.product.model.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class CustomSyncCategoryModel {

    @ExcelProperty(value = "类目级别 一级 二级 三级", index = 0)
    private String categoryType;

    @ExcelProperty(value = "类目ID", index = 1)
    private Long categoryId;

    @ExcelProperty(value = "最低价开始", index = 2)
    private BigDecimal minPriceStart;

    @ExcelProperty(value = "最低价结束", index = 3)
    private BigDecimal minPriceEnd;

    @ExcelProperty(value = "最高价开始", index = 4)
    private BigDecimal maxPriceStart;

    @ExcelProperty(value = "最高价结束", index = 5)
    private BigDecimal maxPriceEnd;
}
