package com.voghion.product.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description 黑白样本任务新增
 * <AUTHOR>
 * @Date 2025/4/18
 **/
@Data
@ApiModel(value = "黑白样本任务新增")
public class BlackSampleTaskAddDTO {

    @ApiModelProperty(value = "商品Ids")
    private String goodsIds;

    @ApiModelProperty(value = "审核人")
    private Long auditUserId;

    @ApiModelProperty(value = "同盾命中品牌")
    private String tongdunAuditBrand;
}
