package com.voghion.product.model.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class AliCategoryMappingModel {
    /**
     * 二级类目ID
     */
    @ExcelProperty(value = "商品id", index = 4)
    private String vgSecondCategoryId;

    /**
     * 末级类目ID
     */
    @ExcelProperty(value = "商品id", index = 8)
    private String vgLastCategoryId;


    /**
     * 阿里末级类目ID
     */
    @ExcelProperty(value = "商品id", index = 9)
    private String aliLastCategoryId;

    /**
     * 阿里二级类目ID
     */
    @ExcelProperty(value = "商品id", index = 11)
    private String aliSecondCategoryId;

}
