package com.voghion.product.model.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

@Data
public class TongDunGoodsImagesDTO {

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 活动id
     */
    private Long goodsId;

    /**
     * 任务id
     */
    private String taskId;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 店铺id
     */
    private Long shopId;

    /**
     * 类目id
     */
    private Long categoryId;

    /**
     * 类目名称
     */
    private String categoryName;

    /**
     * 商品图片
     */
    private String goodsImage;

    /**
     * 审核结果 0 接受 1 失败
     */
    private Integer auditResult;

    /**
     * 违规详情
     */
    private String illegalDetail;

    /**
     * 更新状态 0未开始 1进行中 2已完成
     */
    private Long isType;

    /**
     * 图片唯一id
     */
    private Long imageId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 初始时间
     *
    private LocalDateTime initTime;

    /**
     * Voghion审核结果 0 接受 1 失败
     */
    private Integer selfAuditResult;

    /**
     * 是否精选店铺 0否1是
     */
    private Integer isGold;

    /**
     * 添加方式：0:新增 1:修改 99:临时检测
     * */
    private Integer tdType;
}
