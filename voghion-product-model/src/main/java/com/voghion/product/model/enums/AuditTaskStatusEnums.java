package com.voghion.product.model.enums;

public enum AuditTaskStatusEnums {
    TO_DISTRIBUTE(0, "待分配"),
    TO_AUDIT(1, "待审核"),
    PASS(2, "通过"),
    REJECT(3, "驳回"),
    REVOKE(4, "已撤回"),
    ;

    private Integer code;
    private String msg;

    AuditTaskStatusEnums(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

}
