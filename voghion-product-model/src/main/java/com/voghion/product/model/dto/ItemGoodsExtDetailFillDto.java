package com.voghion.product.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/5/19 18:45
 */
@Data
public class ItemGoodsExtDetailFillDto implements Serializable {

    @ApiModelProperty("商品原始链接")
    private String goodsUrl;

    @ApiModelProperty("商品采购链接")
    private String costUrl;

    @ApiModelProperty("采购供应商")
    private String procureSupplier;

    @ApiModelProperty("原始店铺名称(来源供应商)")
    private String originalShopName;

    @ApiModelProperty("店铺链接")
    private String storeUrl;

    @ApiModelProperty("所属小二")
    private String principal;

    @ApiModelProperty("卖家用户ID")
    private Long sellerUserId;

    @ApiModelProperty("卖家会员账号")
    private String sellerMemberId;

    @ApiModelProperty("卖家店铺名称")
    private String companyName;

}
