package com.voghion.product.model.dto;

import com.voghion.product.model.vo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel
public class FavoritesPageQueryDTO extends PageParam {

    /**
     * 自增id
     */
    @ApiModelProperty(value = "收藏夹id")
    private Long id;
    /**
     * 商品收藏夹名称
     */
    @ApiModelProperty(value = "商品收藏夹名称")
    private String favoritesName;

    /**
     * 创建人名称
     */
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    @ApiModelProperty(value = "创建开始时间")
    private String createStartTime;
    @ApiModelProperty(value = "创建结束时间")
    private String createEndTime;
    @ApiModelProperty(value = "更新开始时间")
    private String updateStartTime;
    @ApiModelProperty(value = "更新结束时间")
    private String updateEndTime;
}
