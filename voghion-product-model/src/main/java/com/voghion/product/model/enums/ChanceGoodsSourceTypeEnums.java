package com.voghion.product.model.enums;

/**
 * @description:
 * @date: 2022/11/24 下午5:23
 * @author: jashley
 */
public enum ChanceGoodsSourceTypeEnums {
    DIRECT_CREATE(1, "直接创建"),
    COPY(2, "克隆"),
    SU_MAI_TONG(3, "速卖通爬虫"),
    SHEIN(4, "shein爬虫"),
    ;

    private final Integer code;

    private final String desc;

    ChanceGoodsSourceTypeEnums(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static String getNameByCode(Integer code) {
        if (code == null) {
            return "";
        }

        for (ChanceGoodsSourceTypeEnums value : ChanceGoodsSourceTypeEnums.values()) {
            if (value.getCode().equals(code)) {
                return value.getDesc();
            }
        }
        return "";
    }


}
