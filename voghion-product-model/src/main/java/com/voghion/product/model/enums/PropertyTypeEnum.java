package com.voghion.product.model.enums;


/**
 * @Description: 产口属性类型
 * @author: shuaibo
 * @Date: 2019/4/16 17:57
 */
public enum PropertyTypeEnum {
    //属性类别 1表示关键属性 2表示销售属性 3表示特殊属性
    PROPERTY_IMPORTANT("1","关键属性"),
    PROPERTY_SALES("2","销售属性"),
    PROPERTY_SPECIAL("3","特殊属性")
    ;
    private String code;
    private String desc;

    PropertyTypeEnum(String code, String desc){
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
