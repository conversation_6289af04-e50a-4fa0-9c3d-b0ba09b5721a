package com.voghion.product.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/5/9 17:59
 */
@Data
public class ItemGoodsSkuInfoDto implements Serializable {

    @ApiModelProperty("skuId")
    private Long skuId;

    /**
     * goodsId + goodsPropertySkuRelevantId
     */
    @ApiModelProperty("SKU规格关联表id拼接")
    private String skuPropertyRelevantStr;

    @ApiModelProperty("sku名称")
    private String name;

    @ApiModelProperty("sku图")
    private String imageUrl;

    @ApiModelProperty("长")
    private String length;

    @ApiModelProperty("宽")
    private String width;

    @ApiModelProperty("高")
    private String height;

    @ApiModelProperty("重量")
    private String weight;

    @ApiModelProperty("重量(单位kg)")
    private Long weightNumber;

    @ApiModelProperty("体积")
    private String packageSize;

    @ApiModelProperty("库存数量")
    private Long stock;

    @ApiModelProperty("价格")
    private BigDecimal price;

    @ApiModelProperty("原始价格")
    private BigDecimal orginalPrice;

    @ApiModelProperty("原划线价")
    private BigDecimal orginalMarketPrice;

    @ApiModelProperty("成本价")
    private BigDecimal costPrice;

    @ApiModelProperty("第三方sku_id")
    private String originalSkuId;

    @ApiModelProperty("默认运费")
    private BigDecimal defaultDelivery;

    @ApiModelProperty("最小购买数量")
    private Integer minPurchaseQuantity;

    @ApiModelProperty("sku的规格映射")
    private Map<String, String> skuProperty;

    // @ApiModelProperty("sku的规格列表")
    // private List<SkuItemGoodsPropertyDto> skuProperty;

}
