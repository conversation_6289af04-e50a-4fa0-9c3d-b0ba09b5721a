package com.voghion.product.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
@ApiModel
public class ReplaceGoodsInfoDTO implements Serializable {
    private static final long serialVersionUID = -459366067578777289L;

    @ApiModelProperty("goodsId")
    private Long goodsId;
    @ApiModelProperty("skuId")
    private Long skuId;
    @ApiModelProperty("商品原始价格")
    private BigDecimal vatBeforePrice;
//    @ApiModelProperty("商品售价")
//    private BigDecimal price;
    @ApiModelProperty("店铺ID")
    private Long shopId;
    @ApiModelProperty("国家")
    private String country;
//    @ApiModelProperty("类目列表包含本级类目")
//    private List<Long> categoryIds;
    @ApiModelProperty("标签列表")
    private List<Long> tagList;

    @ApiModelProperty("itemCode")
    private String itemCode;

    @ApiModelProperty("shopSkuId")
    private String shopSkuId;

    @ApiModelProperty("下单库存数量")
    private Long stock;

}
