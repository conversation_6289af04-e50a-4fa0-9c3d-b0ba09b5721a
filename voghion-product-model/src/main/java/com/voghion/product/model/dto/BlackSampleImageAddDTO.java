package com.voghion.product.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Description 黑白样本图片新增
 * <AUTHOR>
 * @Date 2025/4/18
 **/
@Data
@ApiModel(value = "黑白样本图片新增")
public class BlackSampleImageAddDTO {

    @ApiModelProperty(value = "图片")
    private String imageUrl;

    @ApiModelProperty(value = "品牌")
    private String brand;

    @ApiModelProperty(value = "黑白样本 0黑样本 1白样本")
    private Integer type;
}
