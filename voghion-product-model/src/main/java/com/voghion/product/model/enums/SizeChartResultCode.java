package com.voghion.product.model.enums;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.colorlight.base.model.BaseResultCode;
import lombok.Getter;

/**
 * SizeChartResultCode
 *
 * <AUTHOR>
 * @date 2022/9/19
 */
@Getter
public enum SizeChartResultCode implements BaseResultCode {

    /**
     * 0开头的表示正常
     * 1开头的表示系统异常
     * 11表示商品异常
     * 110表示参数异常
     * 111表示系统异常
     * <p>
     * <p>
     * 2开头的表示参数异常|201开头的表示系统通用参数
     */

    SUCCESS("0000", "SUCCESS", "成功"),
    FAIL("100000", "新增商家模板失败", "新增商家模板失败"),
    NAME_NULL_ERROR("100001", "名称不能为空", "名称不能为空"),
    CATEGORY_NULL_ERROR("100002", "类目不能为空", "类目不能为空"),
    CATEGORY_LEAF_NULL_ERROR("100003", "类目异常", "类目异常"),
    PROP_AREA_NULL_ERROR("100004", "属性/区域至少一个", "属性/区域至少一个"),
    DATA_WEB_NULL_ERROR("100005", "data web不能为空", "data web不能为空"),
    DATA_APP_NULL_ERROR("100006", "data app不能为空", "data app不能为空"),
    SYSTEM_TEMPLATE_EXIST_ERROR("100007", "该类目的系统模板已存在", "该类目的系统模板已存在"),
    SHOP_ID_NULL_ERROR("100008", "查无登录人店铺id", "查无登录人店铺id"),
    TEMPLATE_ID_ERROR("100009", "模板id错误", "模板id错误"),
    TEMPLATE_NOT_EXIST_ERROR("100010", "模板不存在", "模板不存在"),
    SHOP_TEMPLATE_EXIST_ERROR("100011", "模板名称已存在", "模板名称已存在"),
    CATEGORY_UPDATE_ERROR("100012", "适用类目不能修改", "适用类目不能修改"),
    FEELING_PROP_NOT_NULL("100013", "%s该尺码表属性必填", "该尺码表属性必填"),
    FEELING_PROP_CONFIG_IS_NULL("100014", "%s该系统尺码表模板可配置的属性不存在", "该系统尺码表模板可配置的属性不存在"),
    LIMIT_TYPE_NULL("100015", "尺码表规范的限制类型不能为空", "尺码表规范的限制类型不能为空"),
    ALLOW_DEVIATION_NULL("100016", "尺码表规范的允许偏差值不能为空", "尺码表规范的允许偏差值不能为空"),
    ;

    /**
     * 结果码
     */
    private final String code;
    /**
     * 返回消息
     */
    private final String msg;
    /**
     * 描述
     */
    private final String detail;

    SizeChartResultCode(String code, String msg, String detail) {
        this.code = code;
        this.msg = msg;
        this.detail = detail;
    }


    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getDetail() {
        return detail;
    }

    @Override
    public String getDetail(String[] arg0) {
        return null;
    }

    @Override
    public String getMsg() {
        return msg;
    }

    @Override
    public String getName() {
        return name();
    }

    @Override
    public String toString() {
        JSONObject object = new JSONObject();
        object.put("code", code);
        object.put("msg", msg);
        return JSON.toJSONString(object);
    }
}
