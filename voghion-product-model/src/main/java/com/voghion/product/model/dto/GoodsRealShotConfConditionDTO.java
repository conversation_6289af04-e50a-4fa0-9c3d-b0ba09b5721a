package com.voghion.product.model.dto;


import com.voghion.product.model.vo.PageParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class GoodsRealShotConfConditionDTO extends PageParam implements Serializable {


    private static final long serialVersionUID = 1L;


    @ApiModelProperty("卖家id，多个用换行分隔")
    private String shopIds;

    @ApiModelProperty("类目id")
    private Long categoryId;

    @ApiModelProperty("最后更新人")
    private String updateOperator;

    @ApiModelProperty("最后更新时间:start")
    private String startTime;

    @ApiModelProperty("最后更新时间:end")
    private String endTime;


}
