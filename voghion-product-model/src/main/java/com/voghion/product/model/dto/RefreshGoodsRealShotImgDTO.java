package com.voghion.product.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class RefreshGoodsRealShotImgDTO implements Serializable {


    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "商品id")
    private Long goodsId;

    @ApiModelProperty(value = "商品实拍图，全替换:(空/NULL 代表删除商品实拍图)")
    private List<String> realShotImgs;


}
