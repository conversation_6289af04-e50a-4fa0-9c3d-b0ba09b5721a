package com.voghion.product.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class ProhibitionTaskDto implements Serializable {
    private static final long serialVersionUID = -5670094972387333359L;

    /**
     * 禁售/解封id集合
     */
    private Long taskId;

    /**
     * 任务类型 1 解封 2 封禁 3 限流  4 解除限流 5 加权 6 解除加权
     */
    private Integer taskType;

    /**
     * 禁售主体类型 1 商品 2 店铺
     */
    private Integer type;

    private Long goodsId;


}
