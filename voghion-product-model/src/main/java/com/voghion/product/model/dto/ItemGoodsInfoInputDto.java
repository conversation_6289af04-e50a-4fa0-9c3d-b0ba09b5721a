package com.voghion.product.model.dto;

import com.voghion.product.model.vo.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/9 11:50
 */
@Data
public class ItemGoodsInfoInputDto implements Serializable {

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("类目id")
    private Long categoryId;

    @ApiModelProperty(value = "品牌id")
    private Long brandId;

    @ApiModelProperty("第三方商品id")
    private String itemCode;

    @ApiModelProperty("商品名称")
    private String name;

    @ApiModelProperty("商品原始名称")
    private String originalName;

    @ApiModelProperty("描述")
    private String description;

    @ApiModelProperty("商品权重")
    private Long sortValue;

    @ApiModelProperty("商品类型 1单品 2组合商品 3批发商品")
    private Integer type;

    @ApiModelProperty("是否上架 0-下架  1-上架")
    private String isShow;

    @ApiModelProperty("重量")
    private String weight;

    @ApiModelProperty("体积")
    private PackageSizeDTO packageSize;

    @ApiModelProperty("规格信息")
    private List<SpecsDTO> specs;

    @ApiModelProperty("商品主图")
    private String mainImage;

    @ApiModelProperty("商品详情图片")
    private List<String> detailsImgs;

    @ApiModelProperty("商品图片")
    private List<String> goodsImages;

    @ApiModelProperty("商品视频")
    private List<String> goodsVideos;

    @ApiModelProperty("商品实拍图")
    private List<String> realShotImgs;

    @ApiModelProperty(value = "国家")
    private List<String> country;

    @ApiModelProperty("属性信息")
    private List<ItemGoodsPropertyDto> properties;

    @ApiModelProperty(value = "app渠道")
    private Integer appChannel;

    @ApiModelProperty(value = "来源渠道")
    private Integer channel;

    @ApiModelProperty("尺码表模板id")
    private Long sizeChartTemplateId;

    @ApiModelProperty("商品尺码图信息")
    private List<SizeTableDTO> sizeTables;

    @ApiModelProperty("商品原始尺码图信息")
    private List<SizeTableDTO> originalSizeTables;

    /**
     * 后续看看是否可以优化
     * <br/>
     * 属性详情 和 属性模板详情
     */
    @ApiModelProperty("属性详情")
    private List<PropertyGoodsInfoVO> propertyGoodsInfoVOS;

    @ApiModelProperty("属性模板详情")
    private List<PropertyDetailInfoVO> propertyDetailInfoVOList;

    @ApiModelProperty("国家商品说明书详情")
    private List<GoodsManualVO> goodsManualVOList;

    @ApiModelProperty("说明视频")
    private List<GoodsManualVO> goodsManualVideoList;

    @ApiModelProperty("汽配")
    private List<GoodsPartsVO> goodsPartsVOList;

    @ApiModelProperty("操作类型 0-add 1=update")
    private Integer operation = 0;

    @ApiModelProperty("物流属性标签")
    private Integer logisticsProperty;

    @ApiModelProperty("是否设置国家运费")
    private Boolean IsDefaultDelivery;

    @ApiModelProperty("国家运费")
    private List<GoodsFreightVO> freightList;

    @ApiModelProperty("商品sku")
    private List<ItemGoodsSkuInfoDto> skuInfo;

    @ApiModelProperty("应用类型 0/null:普通商品、 1:listing模板、 2:listing商家商品")
    private Integer useType = 0;

    @ApiModelProperty("填充到goods对象")
    private ItemGoodsFillDto itemGoodsFill;

    @ApiModelProperty("填充到goodsExtDetail对象")
    private ItemGoodsExtDetailFillDto goodsExtDetailFill;

    @ApiModelProperty("销量")
    private Long sales;

    @ApiModelProperty("商品附属信息")
    private List<GoodsExtraPropertyVO> goodsExtraPropertyVOS;

    @ApiModelProperty("是否有建议降价")
    private Boolean isReductionPrice;

    // 需要理解一下为啥不直接用操作人
    @ApiModelProperty("上新负责人")
    private String arrival;

    @ApiModelProperty("草稿箱id")
    private Long draftId;

    // === ↓买手操作标记↓ ===
    @ApiModelProperty("店铺id")
    private Long shopId;

    @ApiModelProperty("店铺名")
    private String storeName;

    @ApiModelProperty("买手token")
    private String buyerClientinfo;
    // === ↑买手操作标记↑ ===

    /**
     * 操作日志类型
     * <br/>
     * see com.voghion.product.api.enums.OperationLogTypeEnums
     */
    private Integer operationLogType;

    @ApiModelProperty("是否检查店铺信息")
    private boolean needCheckShop = true;

    @ApiModelProperty("数据来源 0 voghion平台 1 scm平台")
    private Integer sourceType = 0;

    @ApiModelProperty("尺码图")
    private String sizeImage;

    // ==== ↓scm同步时用↓ ====

    @ApiModelProperty("尺码图信息")
    private SizeChartTemplateVo sizeChartTemplateVo;

    // 三方导入数据的时候会给值
    @ApiModelProperty("是否为任务")
    private Boolean isTask;

    /**
     * 当前只有更新的时候有使用
     * 0:非精选商品 1:是精选商品
     */
    private Integer isPickedShopGoods;

}
