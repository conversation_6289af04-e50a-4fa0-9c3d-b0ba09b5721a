package com.voghion.product.model.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.voghion.product.model.po.GoodsFreight;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class FavoritesGoodsRecordDTO {

    private Long id;

    /**
     * 收藏夹id
     */
    private Long favoritesId;

    /**
     * 店铺id
     */
    private Long shopId;

    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 商品id
     */
    private Long goodsId;

    /**
     * 商品主图
     */
    private String mainImage;

    /**
     * 类目id
     */
    private Long categoryId;

    /**
     * 类目名称
     */
    private String categoryName;

    /**
     * 商品最高价
     */
    private BigDecimal maxPrice;

    /**
     * 商品最低价
     */
    private BigDecimal minPrice;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 是否上架
     */
    private String isShow;

    /**
     * 国家运费
     */
    private List<GoodsFreightDTO> countryFreight;

    /**
     * 锁定标签信息
     */
    private String lockLabelInfo;

    /**
     * 近30天gmv
     */
    private BigDecimal gmv;

    /**
     * 报名的活动信息
     */
    private String signActivityInfo;

    /**
     * 正在参与的活动信息
     */
    private String joinActivityInfo;

    /**
     * 收藏状态 1 - 正常 0 - 删除
     */
    private Integer favoriteStatus;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 商品添加人名称
     */
    private String createUserName;

    /**
     * 添加人id
     */
    private Long createUserId;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 更新时间
     */
    private String updateTime;
}
