package com.voghion.product.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class ProhibitionQueryDto implements Serializable {
    private static final long serialVersionUID = -40077238681808160L;
    /**
     * 状态 1表示解封 2 表示封禁中
     */
    private Integer status;

    private Integer goodsId;

    private String shopName;

    private Integer shopId;

    private Integer pageSize;

    private Integer pageNow;

    /**
     * 商品id集合字符串
     */
    private String goodsIdListStr;

    private List<Long> goodsIds;

    /**
     * 禁售原因
     */
    private String reason;

    /**
     * 创建开始时间
     */
    @ApiModelProperty("创建开始时间")
    private Date createStartTime;

    /**
     * 创建结束时间
     */
    @ApiModelProperty("创建结束时间")
    private Date createEndTime;

    /**
     * 创建开始时间
     */
    @ApiModelProperty("更新开始时间")
    private Date updateStartTime;

    /**
     * 创建结束时间
     */
    @ApiModelProperty("更新结束时间")
    private Date updateEndTime;

    @ApiModelProperty("创建人")
    private String createUser;
}
