package com.voghion.product.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 机会商品模板历史记录表
 * 用于追踪商品进入和退出榜单的时间戳
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Data
public class ChanceGoodsTemplateHistory implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 机会商品模板ID
     */
    private Long templateId;

    /**
     * 商品ID
     */
    private Long goodsId;

    /**
     * 榜单类型 8-周榜, 9-月榜, 10-当前季度, 11-上1季度, 12-上2季度, 13-上3季度
     */
    private Integer rankingType;

    /**
     * 操作类型 1-进入榜单, 2-退出榜单
     */
    private Integer operationType;

    /**
     * 排名
     */
    private Integer rank;

    /**
     * 热度值
     */
    private Integer hot;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 类目ID
     */
    private Long categoryId;

    /**
     * 商品主图
     */
    private String mainImage;

    /**
     * 最低价
     */
    private BigDecimal minPrice;

    /**
     * 最高价
     */
    private BigDecimal maxPrice;

    /**
     * 近七日销量
     */
    private Integer sevenSales;

    /**
     * 操作时间
     */
    private Date operationTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 备注信息
     */
    private String remark;

    /**
     * 是否删除 0-否 1-是
     */
    private Integer isDel;

}
