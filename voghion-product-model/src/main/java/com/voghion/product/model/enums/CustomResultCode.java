package com.voghion.product.model.enums;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.colorlight.base.model.BaseResultCode;

import java.util.List;

/**
 * @description:
 * @date: 2022/3/7 10:07
 * @author: jashley
 */
public class CustomResultCode implements BaseResultCode {
    private static final long serialVersionUID = 2970762260461167303L;

    /**
     * 结果码
     */
    private final String code;
    /**
     * 返回消息
     */
    private final String msg;
    /**
     * 描述
     */
    private final String detail;

    public CustomResultCode(String code, String msg, String detail) {
        this.code = code;
        this.msg = msg;
        this.detail = detail;
    }

    public static CustomResultCode fill(BaseResultCode resultCode, String fillContent) {
        return new CustomResultCode(resultCode.getCode(), StringUtils.format(resultCode.getMsg(), fillContent), resultCode.getDetail());
    }

    public static CustomResultCode fill(BaseResultCode resultCode, List<String> objects) {
        return new CustomResultCode(resultCode.getCode(), StringUtils.format(resultCode.getMsg(), objects.toArray()), resultCode.getDetail());
    }


    @Override
    public String getCode() {
        return this.code;
    }

    @Override
    public String getMsg() {
        return this.msg;
    }

    @Override
    public String getDetail() {
        return this.detail;
    }

    @Override
    public String getDetail(String[] params) {
        return null;
    }

    @Override
    public String getName() {
        return null;
    }

    @Override
    public String toString() {
        JSONObject object = new JSONObject();
        object.put("code", code);
        object.put("msg", msg);
        return JSON.toJSONString(object);
    }
}
