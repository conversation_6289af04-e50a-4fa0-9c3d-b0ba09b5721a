package com.voghion.product.model.enums;

import com.google.common.collect.Lists;

import java.util.List;

public enum PropertyDetailTypeEnums {
    SINGLE_CHOICE(1,"单选"),
    PICK_ONE_OF_TWO(2,"二选一"),
    MULTIPLE_CHOICE(3,"复选"),
    PURE_DIGITAL(4,"自由填写(仅数字)"),
    PURE_LETTERS(5,"自由填写(仅英文)"),
    DIGITAL_AND_LETTERS(6,"自由填写(数字+英文)"),
    OPEN_SINGLE_CHOICE(7,"自由填写+单选"),
    OPEN_MULTI_CHOICE(8,"自由填写+多选"),
    EVENT_DATE(9,"日期"),
    ;

    /**
     * 状态
     */
    private Integer code;

    /**
     * 描述
     */
    private String desc;

    PropertyDetailTypeEnums(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static PropertyDetailTypeEnums getEnumByCode(Integer code) {
        for (PropertyDetailTypeEnums enums : PropertyDetailTypeEnums.values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return null;
    }

    //获取支持输入单位的控件类型
    public static List<Integer> getSupUnitType(){
        return Lists.newArrayList(PURE_DIGITAL.getCode(), PURE_LETTERS.getCode(), DIGITAL_AND_LETTERS.getCode(),
                OPEN_SINGLE_CHOICE.getCode(), OPEN_MULTI_CHOICE.getCode(), EVENT_DATE.getCode());
    }
}
