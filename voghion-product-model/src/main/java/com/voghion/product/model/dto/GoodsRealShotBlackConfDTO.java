package com.voghion.product.model.dto;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class GoodsRealShotBlackConfDTO implements Serializable {


    private static final long serialVersionUID = 1L;


    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("店铺标签ID")
    private String tagId;

    @ApiModelProperty("国家集合，多个用换行分隔")
    private String countries;

    @ApiModelProperty("类目id集合，多个用换行分隔")
    private String categoryIds;

    @ApiModelProperty(value = "状态 1:有效 0：无效",required = true)
    private Integer status;

}
