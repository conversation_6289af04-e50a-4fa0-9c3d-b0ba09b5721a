package com.voghion.product.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/14 15:59
 */
@Data
public class SkuItemGoodsPropertyDto {

    @ApiModelProperty("商品规格关联表 id")
    private Long id;

    @ApiModelProperty("是否为主规格")
    private boolean mainProperty;

    @ApiModelProperty("商品规格名称")
    private String name;

    @ApiModelProperty("商品规格名称排序")
    private Integer nameSort;

    @ApiModelProperty("商品规格值名称")
    private String value;

    @ApiModelProperty("商品规格值排序")
    private Integer valueSort;

    @ApiModelProperty("商品规格值携带的图片(主规格必填，非主规格没有)")
    private List<ItemGoodsPropertyImgDto> imgList;

}
