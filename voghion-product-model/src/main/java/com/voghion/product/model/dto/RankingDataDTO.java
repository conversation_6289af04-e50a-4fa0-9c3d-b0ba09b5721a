package com.voghion.product.model.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 榜单数据传输对象
 * 用于从ES索引获取榜单数据
 * 
 * <AUTHOR>
 * @since 2024-12-19
 */
@Data
public class RankingDataDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 商品ID
     */
    private Long goodsId;

    /**
     * 热度值
     */
    private Integer hot;

    /**
     * 排名
     */
    private Integer rank;

    /**
     * 榜单类型 8-周榜, 9-月榜, 10-当前季度, 11-上1季度, 12-上2季度, 13-上3季度
     */
    private Integer type;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 类目ID
     */
    private Long categoryId;

    /**
     * 商品主图
     */
    private String mainImage;

    /**
     * 近七日销量
     */
    private Integer sevenSales;

    /**
     * 数据来源索引名称
     */
    private String sourceIndex;

    /**
     * 数据同步时间戳
     */
    private Long syncTimestamp;
}
