package com.voghion.product.model.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.colorlight.base.model.PageView;
import lombok.Data;

import java.time.LocalDateTime;


@Data
public class PropertyStandardInfoDTO extends PageView<PropertyStandardInfoDTO> {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 规范名称
     */
    private String standard;

    /**
     * 类目id
     */
    private Long categoryId;

    /**
     * 类目名称
     */
    private String categoryName;

    /**
     * 属性名称
     */
    private String propertyName;

    /**
     * 创建人
     */
    private String createUserName;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 最后修改人
     */
    private String updateUserName;

    /**
     * 最后修改时间
     */
    private LocalDateTime updateTime;


    /**
     *
     * 筛选过滤项字段
     * @return
     */
    private Boolean hasFilterFunction;

}
