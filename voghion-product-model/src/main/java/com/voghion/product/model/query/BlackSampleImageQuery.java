package com.voghion.product.model.query;

import com.colorlight.base.model.PageView;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @Description 黑白样本图片查询
 * <AUTHOR>
 * @Date 2025/4/18
 **/
@Data
@ApiModel(value = "黑白样本图片查询")
public class BlackSampleImageQuery {

    @ApiModelProperty(value = "样本ID")
    private String idListStr;

    @ApiModelProperty(value = "商品ID")
    private String goodsIdListStr;

    @ApiModelProperty(value = "类目ID")
    private Long categoryId;

    @ApiModelProperty(value = "人工判断类型 0黑样本 1白样本")
    private Integer artificialJudgeType;

    @ApiModelProperty(value = "自研判断类型 0黑样本 1白样本")
    private Integer algorithmJudgeType;

    @ApiModelProperty(value = "品牌名称")
    private String brandName;

    @ApiModelProperty(value = "状态 0无效 1有效")
    private Integer status;

    @ApiModelProperty(value = "开始审核时间")
    private Date startAuditTime;

    @ApiModelProperty(value = "结束审核时间")
    private Date endAuditTime;

    private Integer pageNow = 1;
    private Integer pageSize = 20;
}
