package com.voghion.product.model.enums;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.colorlight.base.model.BaseResultCode;

public enum ActivityResultCode implements BaseResultCode {
    OPERATOR_PARAMETER_NULL("4041111", "操作人信息为空", "操作人信息为空"),
    ACTIVITY_EFFECT_TIME_ERROR("4041112", "生效时间与其他活动冲突", "生效时间与其他活动冲突"),
    DISCOUNT_ERROR("4041113", "最低折扣参数异常", "最低折扣参数异常"),
    TIME_ERROR("4041114", "报名时间段跟生效时间段冲突", "报名时间段跟生效时间段冲突"),
    NUMBER_ERROR("4041115", "可报名数量无效", "可报名数量无效"),
    TIME_PARAMETER_ERROR("4041116", "生效时间与%s活动重叠，请重新选择", "生效时间与活动重叠，请重新选择"),
    NAME_ERROR("4041117", "名称重复", "名称重复"),
    TYPE_NULL_ERROR("4041118", "活动类型不能为空:1商家报名 2七日达", "活动类型不能为空:1商家报名 2七日达"),
    GOODS_ID_LIST_NULL_ERROR("4041119", "活动id集合不能空", "活动id集合不能空"),
    ACTIVITY_TYPE_NOT_NULL("4041120", "活动类型不能为空", "活动类型不能为空")

    ;



    /**
     * 结果码
     */
    private final String code;
    /**
     * 返回消息
     */
    private final String msg;
    /**
     * 描述
     */
    private final String detail;

    ActivityResultCode(String code, String msg, String detail) {
        this.code = code;
        this.msg = msg;
        this.detail = detail;
    }

    public String getCode() {
        return code;
    }

    public String getDetail() {
        return detail;
    }

    public String getDetail(String[] arg0) {
        return null;
    }

    public String getMsg() {
        return msg;
    }

    public String getName() {
        return name();
    }

    @Override
    public String toString() {
        JSONObject object = new JSONObject();
        object.put("code", code);
        object.put("msg", msg);
        return JSON.toJSONString(object);
    }

    public static CustomResultCode fill(ActivityResultCode resultCode, String fillContent) {

        return new CustomResultCode(resultCode.getCode(), String.format(resultCode.getMsg(), fillContent), resultCode.getDetail());
    }
}
