package com.voghion.product.model.enums;

public enum DimensionTypeEnum {
    SHOP(1, "店铺"),
    CATEGORY(2, "类目"),
    ;

    private Integer code;
    private String desc;

    DimensionTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return this.desc;
    }

    public static DimensionTypeEnum getEnumsByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (DimensionTypeEnum dimensionTypeEnum : values()) {
            if (code.equals(dimensionTypeEnum.getCode())) {
                return dimensionTypeEnum;
            }
        }
        return null;
    }
}