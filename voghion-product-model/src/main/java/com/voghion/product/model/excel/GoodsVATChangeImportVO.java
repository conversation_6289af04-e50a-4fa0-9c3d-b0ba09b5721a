package com.voghion.product.model.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class GoodsVATChangeImportVO {
    @ExcelProperty(value = "商品id", index = 0)
    private Long goodsId;
    @ExcelProperty(value = "操作 1 有效 0 无效", index = 1)
    private Integer effectStatus;
    @ExcelProperty(value = "vat", index = 2)
    private BigDecimal vat;
}
