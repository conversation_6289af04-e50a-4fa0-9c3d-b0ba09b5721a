package com.voghion.product.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Description 黑白样本图片状态更新
 * <AUTHOR>
 * @Date 2025/4/18
 **/
@Data
@ApiModel(value = "黑白样本图片状态更新")
public class BlackSampleImageStatusUpdateDTO {

    @ApiModelProperty(value = "样本ID")
    private Long id;

    @ApiModelProperty(value = "状态 0无效 1有效")
    private Integer status;
}
