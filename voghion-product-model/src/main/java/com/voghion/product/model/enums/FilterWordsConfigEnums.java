package com.voghion.product.model.enums;

import com.colorlight.base.utils.CheckUtils;

public enum FilterWordsConfigEnums {
    CONFIG_MAIN_NAME(1, "主词"),
    CONFIG_MINOR_NAME(2,"副词"),
    CONFIG_LABEL(3, "标签"),
    CONFIG_RISK_LEVEL(4, "风险等级"),
    CONFIG_TAG_ID(5, "商品打标"),
    CONFIG_CHANGE_STATUS(6, "商品状态变更"),
    CONFIG_ALLOW_SEARCH(7, "允许搜索"),
    CONFIG_SOURCE(8, "来源"),
    CONFIG_IS_VALID(9, "是否有效"),
    CONFIG_MATCH_RESULT(10, "命中后效果"),
    CONFIG_CATEGORY_TYPE(11, "类目配置方式"),
    CONFIG_CATEGORY_IDS(12, "类目配置id"),
    REFRESH_MINOR_CONFIG(13, "主词修改刷新所属副词"),
    ;

    private Integer code;
    private String name;

    FilterWordsConfigEnums(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }


    public static String getTaskTypeNameByCode(Integer code) {
        for (FilterWordsConfigEnums typeEnum : FilterWordsConfigEnums.values()) {
            if (typeEnum.getCode().equals(code)) {
                return typeEnum.getName();
            }
        }
        CheckUtils.check(true, ProductResultCode.AUDIT_TASK_TYPE_INVALID);
        return null;
    }
}