package com.voghion.product.model.enums;

public enum EsEnums {
    GOODS_ES("goods_es_new_v2", "_doc"),
    ES_INDEX_INFO_ENUMS("current_index", "_doc"),
    GCR_LISTING_ES_INDEX_INFO_ENUMS("gcr_listing_current_index", "_doc"),
    GOODS_ES_LOG("goods_es_log", "_DOC"),
    GOODS_ES_GOODS_LOG("goods_es_goods_log", "_DOC"),
    GOODS_ES_ITEM_LOG("goods_es_goods_item_log", "_DOC"),
    GOODS_ES_FREIGHT_LOG("goods_es_goods_freight_log", "_doc"),
    GOODS_VAT_ES("goods_vat_es", "_doc"),
    GCR_ES_INDEX_INFO_ENUMS("gcr_current_index", "_doc"),
    KEYWORD("keyword", "_doc"),
    GOODS_EVERY_DAY_ES_A("goods_every_day_es_a", "_doc"),
    GOODS_EVERY_DAY_ES_B("goods_every_day_es_b", "_doc"),
    GOODS_EVERY_DAY_INDEX("goods_every_day_index", "_doc"),
    GOODS_ID_ES_A("goods_id_es_a", "_doc"),
    GOODS_ID_ES_B("goods_id_es_b", "_doc"),
    GOODS_ID_ENUMS("goods_id_index", "_doc"),
    GOODS_COMMENT_INDEX("goods_comment_index", "_doc"),
    CATEGORY_AVG_PRICE_INDEX("category_avg_price_index", "_doc"),
    GOODS_UP_DOWN_INDEX("goods_up_down_index", "_doc"),
    TAG_ES("goods_tag_es", "_doc"),
    GOODS_NEW_ES("goods_new_es", "_doc"),
    GOODS_EVERY_DAY_ENUMS("goods_every_day_index", "_doc"),
    SHOP_BOARD_ES("shop_board_es", "_doc"),
    SHOP_ASSESSMENT_ES("shop_assessment_es", "_doc"), //店铺综合星级新es

    GOODS_GLOBAL_PROPERTY_ES("goods_global_property_es", "_doc"),

    ;

    private String index;
    private String type;

    EsEnums(String index, String type) {
        this.index = index;
        this.type = type;
    }

    public String getIndex() {
        return index;
    }

    public void setIndex(String index) {
        this.index = index;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
}
