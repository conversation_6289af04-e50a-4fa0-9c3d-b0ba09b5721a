package com.voghion.product.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * 店铺禁售：手动操作类型
 */
@Getter
@AllArgsConstructor
public enum ShopOptTypeEnum {

    ADD_LIMIT(1, "新增限流"),
    ADD_PROHIBITION(2, "新增禁售"),
    REMOVE_LIMIT(3, "解除限流"),
    REMOVE_PROHIBITION(4, "解除禁售")

    ;


    private final Integer type;
    private final String desc;

    public static ShopOptTypeEnum getByType(Integer type) {
        return Arrays.stream(ShopOptTypeEnum.values())
                .filter(typeEnum -> Objects.equals(type, typeEnum.getType()))
                .findAny().orElse(null);
    }
}
