package com.voghion.product.model.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class FavoritesRecordDTO {
    private static final long serialVersionUID = 1L;

    /**
     * 自增id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 商品收藏夹名称
     */
    private String favoritesName;

    /**
     * 商品收藏数
     */
    private Long goodsNums;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 创建人名称
     */
    private String createUserName;

    /**
     * 创建人id
     */
    private Long createUserId;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 更新时间
     */
    private String updateTime;
}
