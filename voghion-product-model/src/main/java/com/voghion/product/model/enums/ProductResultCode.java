package com.voghion.product.model.enums;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.colorlight.base.model.BaseResultCode;

/**
 * 业务返回码
 * 
 * 系统的为0000开头的
 * 
 * <AUTHOR>
 * @version $Id: BussinessResultCode.java, v 0.1 2017年2月18日 下午8:47:14 sky Exp $
 */
public enum ProductResultCode implements BaseResultCode{

    /**
     * 0开头的表示正常
     * 1开头的表示系统异常
     * 11表示商品异常
     * 110表示参数异常
     * 111表示系统异常
     *
     *
     * 2开头的表示参数异常|201开头的表示系统通用参数
     *
     *
     */

    SUCCESS("0000", "SUCCESS", "成功"),
    LOGIN_ERROR("500001","登录失效，请重新登录","登录失效，请重新登录"),
    PARAMETER_ERROR("1100000", "参数异常", "参数异常"),
    PARAMETER_ERROR_EXT("1100000", "(%s)参数异常", "参数异常"),
    PARAMETER_IS_NULL_EXT("1100001", "参数(%s)不得为空", "参数(%s)不得为空"),
    PARAMETER_ID_ERROR("1100201", "ID参数异常", "Id参数异常"),
    PARAMETER_GOODS_ID_NULL("1100202", "商品id不得为空", "商品id不得为空"),
    FAIL("1100203", "接口失效", "接口失效"),
    TRANSLATE_FAIL("1100204", "翻译失败", "翻译失败"),
    TRANSLATE_FAIL_EXT("1100204", "翻译失败(%s)", "翻译失败"),
    EXECUTE_FAIL("1100205", "执行失败", "执行失败"),
    TASK_NUM_NULL_ERROR("1100206", "分配数量不能为空", "分配数量不能为空"),
    TRY_LOCK_FAIL("1100207", "正在处理中，请勿重复操作", "正在处理中，请勿重复操作"),

    LIMIT_100("1100300", "单词限制100条", "单词限制100条"),
    LIMIT_500("1100301", "单词限制500条", "单词限制500条"),
    LIMIT_1000("1100302", "单词限制1000条", "单词限制1000条"),
    LIMIT_3000("1100303", "单词限制3000条", "单词限制3000条"),
    LIMIT_10000("1100304", "单词限制10000条", "单词限制10000条"),

    NULL_ERROR("1110001", "商品对象为空", "商品对象为空"),
    NAME_AS_ERROR("1110001", "商品解析异常【商品名称】", "商品解析异常【商品名称】"),
    MINPRICE_AS_ERROR("1110002", "商品解析异常【最低价格】", "商品解析异常【最低价格】"),
    MAXPRICE_AS_ERROR("1110003", "商品解析异常【最高价格】", "商品解析异常【最高价格】"),
    MARKET_AS_ERROR("1110004", "商品解析异常【划线价格】", "商品解析异常【划线价格】"),
    BANNER_AS_ERROR("1110005", "商品解析异常【轮播图】", "商品解析异常【轮播图】"),
    SPEC_AS_ERROR("1110006", "商品解析异常【商品规格】", "商品解析异常【商品规格】"),
    PRICE_AS_ERROR("1110007", "商品解析异常【销售价格】", "商品解析异常【销售价格】"),
    TRY_AS_ERROR("1110008", "商品解析异常【重复导入】", "商品解析异常【重复导入】"),
    CATEGORY_NULL_ERROR("1110009", "类目为空", "类目为空"),
    ADD_PRODUCT_ERROR("1110010", "添加产品信息失败", " 添加产品信息失败"),
    CHECK_PROPERTY_ERROR("1110011", "属性必须有属性值", " 属性必须有属性值"),
    ADD_CATEGORY_ERROR("1110012", "添加类目失败", " 添加类目失败"),
    ADD_GOODS_EXT_DETAIL_ERROR("1110013", "添加商品详情扩展失败", " 添加商品详情扩展失败"),
    ADD_GOODS_TMP_ERROR("1110014", "添加商品TMP失败", " 添加商品TMP失败"),
    ADD_GOODS_ITEM_ERROR("1110015", "添加商品子项失败", " 添加商品子项失败"),
    PRICE_LESS_ERROR("1110016", "最低价格不能小于0", "最低价格不能小于0"),
    PRICE_ABOVE_MAX_ERROR("1110017", "最低价格不能大于最高价格", "最低价格不能大于最高价格"),
    ADD_PROPERTY_ERROR("1110018", "添加属性失败", " 添加属性失败"),
    ADD_PROPERTY_VALUE_ERROR("1110019", "添加属性值失败", " 添加属性值失败"),
    ADD_PRODUCT_SKU_ERROR("1110020", "添加产品信息失败", " 添加产品信息失败"),
    ADD_PRODUCT_ID_ERROR("1110028", "productID 参数异常", " productID 参数异常"),
    UPDATE_ERROR("1110029", "更新失败", "更新失败"),
    ADD_ERROR("11100230", "添加失败", "添加失败"),
    CREATE_SKU_ERROR("1110021", "生成的sku信息失败", "生成的sku信息失败"),
    GOODS_NOT_EXIST("1110022", "商品不存在", "商品不存在"),
    GOODS_NOT_EXIST_EXT("1110022", "%s商品不存在", "商品不存在"),
    GOODS_DETAIL_NOT_EXIST("1110023", "商品详情查询失败", "商品详情查询失败"),
    GOODS_ALREADY_DELETE("1110024", "该商品已删除", "该商品已删除"),
    CATEGORY_NOT_FIND("1110025", "找不到该类目", "找不到该类目"),
    CATEGORY_IS_DELETE("1110026", "该类目已删除", "该类目已删除"),
    GOODS_SKU_EMPTY("1110030", "商品sku信息为空", "商品sku信息为空"),
    PRODUCT_SKU_ERROR("1110031", "商品sku参数错误", "商品sku参数错误"),
    SELECT_ERROR("1110032", "查询失败", "查询失败"),
    GOODS_SKU_PRICE_ERROR("1110033", "sku价格参数异常", "sku价格参数异常"),
    GOODS_SKU_STORK_ERROR("1110034", "sku库存参数异常", "sku库存参数异常"),
    GOODS_SKU_COST_PRICE_ERROR("1110035", "sku采购价参数异常", "sku采购价参数异常"),
    GOODS_SKU_NAME_ERROR("1110034", "sku名称参数异常", "sku名称参数异常"),
    CHECK_PROPERTY_INFO_ERROR("1110035", "请检查 是否有属性值，属性 属性值是否重复", " 请检查 是否有属性值，属性 属性值是否重复"),
    GOODS_TAG_UPDATE_IDS_NULL_ERROR("1110036", "商品ID列表不能为空", "商品ID列表不能为空"),
    GOODS_TAG_UPDATE_TAG_NULL_ERROR("1110037", "标签不能为空", "标签不能为空"),
    IMPORT_ERROR("1110038", "导入失败", "导入失败"),
    USER_EMPTY("1110039", "商户id为空", "商户id为空"),
    CATEGORY_ERROR("1110040", "类目参数错误", "类目参数错误"),
    ITEM_CODE_ERROR("1110041", "商家商品ID未输入", "商家商品ID未输入"),
    SHOP_SKU_ERROR("1110042", "商家SKUID未输入", "商家SKUID未输入"),
    SKU_NULL_ERROR("1110043","原始skuId不能为空","原始skuId不能为空"),
    GOODS_CODE_NULL_ERROR("1110044","原始商品Id不能为空","原始商品Id不能为空"),
    GOODS_DUPLICATE("1110045","该商品编码已存在，请勿重复添加","该商品编码已存在，请勿重复添加"),
    GOODS_IS_LOCKED("1110046","商品已经上锁，请联系管理员","商品已经上锁，请联系管理员"),
    GOODS_IS_LOCKED_EXT("1110046","商品(%s)已经上锁，无法操作","商品(%s)已经上锁，无法操作"),
    MAIN_IMAGE_ERROR("1110047", "主图不允许为空", "主图不允许为空"),
    GOODS_IMAGE_ERROR("11100471", "商品图片不允许为空", "商品图片不允许为空"),
    GOODS_IMAGE_FORMAT_ERROR("11100472", "图片格式不正确", "图片格式不正确"),

    GOODS_IMAGE_FORMAT_ERROR_HTML("11100473", "图片格式不允许以html/gif结尾：%s", "图片格式不允许以html/gif结尾"),

    PROPERTY_GOODS_IMAGE_REPEAT_ERROR("11100474", "不可以在多个规格上都传递图片", "不可以在多个规格上都传递图片"),
    DETAIL_IMAGE_ERROR("1110048", "详情图不允许为空", "详情图不允许为空"),
    SIZE_IMAGE_ERROR("1110049", "此类目商品，尺码表和尺码图，必须至少选其中一样填写", "此类目商品，尺码表和尺码图，必须至少选其中一样填写"),
    SIZE_CHART_TEMPLATE_MUST("1110049", "此类目必须上传尺码表，请维护后重新上传", "此类目必须上传尺码表，请维护后重新上传"),

    WEIGHT_ERROR("1110050", "重量体积参数异常", "重量体积参数异常"),
    LOGISTICS_PROPERTY_NULL("1110050", "物流属性参数必填", "物流属性参数必填"),
    LOGISTICS_SCENE_NULL("1110050", "物流场景参数必填", "物流场景参数必填"),
    COUNTRY_UNMAINTAINED ("1110051", "选择的国家未维护，请自行输入运费", "重选择的国家未维护，请自行输入运费"),
    PRICE_ERROR("1110052", "sku价格必须大于0", "sku价格必须大于0"),
    DEFAULT_DELIVERY_PRICE_ERROR("1110053", "默认运费价格不为空且不允许为负数", "默认运费价格不为空且不允许为负数"),
    CURRENT_DELIVERY_PRICE_ERROR("1110054", "国家运费价格不允许为负数", "国家运费价格不允许为负数"),
    CURRENT_DELIVERY_PRICE_NULL("1110054", "国家运费价格不允许为空", "国家运费价格不允许为空"),
    CATEGORY_MAPPING_ERROR("1110055","找不到匹配的映射类目","找不到匹配的映射类目"),
    QUAlITY_STORE_NOT_EXIST("1110056","该精品店铺信息不存在","该精品店铺信息不存在"),
    ADD_GOODS_BELONG_MULTIPLE_SHOP("1110057","添加的橱窗商品来自多个店铺","添加的橱窗商品来自多个店铺,请重新确认后再添加"),
    QUALITY_STORE_ADD_NEED_CONFIRM("1110058","橱窗商品可以添加，请确认","橱窗商品可以添加，请确认"),
    QUALITY_STORE_IMPORT_LIMIT("1110059","橱窗商品导入最多支持6个商品","橱窗商品导入最多支持6个商品"),
    GOODS_IS_NOT_LOCKED("1110060","商品未上锁，无法调价","商品未上锁，无法调价"),
    DIFF_GOODS_ID_WRONG("1110061","商品ID不唯一，请确认后操作","商品ID不唯一，请确认后操作"),
    LOCKED_GOODS_PRICE_CAN_ONLY_LESS("1110062","锁定中商品价格只能下调","锁定中商品价格只能下调"),
    LOCKED_GOODS_STOCK_CAN_ONLY_MORE("1110062","锁定中商品库存只能上调","锁定中商品库存只能上调"),
    EXPORT_LIMIT("1110066","导出限制1000个商品，请调整","导出限制1000个商品，请调整"),
    ACTIVITY_NOT_EXIST("1110067","该活动不存在","该活动不存在"),
    NOT_IN_SIGN_TIME("1110068","当前不在报名时间内","当前不在报名时间内"),
    NOT_FIND_USER_INFO("1110069","获取不到登录用户信息","获取不到登录用户信息"),
    EXIST_GOODS_NOT_BELONG_SHOP("1110070","%s商品不属于当前店铺","存在不属于当前店铺的商品"),
    EXIST_GOODS_NOT_BELONG_SHOP_EXT("1110070","该商品不属于当前店铺","存在不属于当前店铺的商品"),
    BIGGER_THAN_ACTIVITY_MIN_DISCOUNT("1110071","当前商品折扣不得大于活动设置最低折扣","当前商品折扣不得大于活动设置最低折扣"),
    GOODS_COUNT_OVER_LIMIT("1110072","商品数量超出限制","商品数量超出限制"),
    GOODS_NOT_CONFORM("1110073","%s商品不符合报名条件","商品不符合报名条件"),
    ACTIVITY_ID_NULL("1110074","活动id不得为空","活动id不得为空"),
    ACTIVITY_EXIST_CONFIG("1110075","该活动已存在配置信息","该活动已存在配置信息"),
    GOODS_CATEGORY_NOT_CONFORM("1110076","%s商品所属类目不符合活动要求","商品所属类目不符合活动要求"),
    GOODS_IS_NOT_SHOW("1110077","%s商品未上架","商品未上架"),
    GOODS_SKU_MUST_EXIST_STOCK("1110078","%s商品所有SKU无库存","商品至少有一个SKU库存>0"),
    GOODS_ALREADY_SIGN("1110079","%s商品已参加过本次活动","存在商品已参加过本次活动"),
    LOCK_ITEM_REDUCE_STOCK("1110080","%s商品已锁定，不能减少库存","锁定商品不能减少库存"),
    EXCEL_PARAM_ERROR("1110081","导入文件中%s商品数据错误，请检查后重新上传","商品数据错误，请检查后重新上传"),
    GOODS_IS_PROHIBITE("1110082","商品已经禁售，无法进行操作","商品已经禁售，无法进行操作"),
    COUNTRY_CODE_NOT_EXIST("1110083","系统国家不匹配，请检查后输入正确的国家二字码","系统国家不匹配，请检查后输入正确的国家二字码"),
    SAME_COUNTRY_FREIGHT_EXIST("1110084","存在相同国家的国家运费","存在相同国家的国家运费"),
    VAT_RATE_NOT_FOUND("1110085","找不到vat税费","找不到vat税费"),
    GOODS_FREIGHT_EMPTY("1110087","国家运费必填","国家运费必填"),
    VAT_RATE_FOR_COUNTRY_EXIST("1110086","该国家VAT费率已存在","该国家VAT费率已存在"),
    VAT_RATE_FOR_CATEGORY_EXIST("1110086","该类目VAT费率已存在","该类目VAT费率已存在"),
    VAT_RATE_FOR_INCREASE_EXIST("1110086","该叠加VAT费率已存在","该叠加VAT费率已存在"),
    VAT_RATE_FOR_COUNTRY_NOT_EXIST("1110087","该国家VAT费率不存在","该国家VAT费率不存在"),
    CATEGORY_MUST_BE_LEAF("1110088","%s类目不是叶子类目，请检查后重试","类目必须为叶子类目，请检查后重试"),
    SHOP_IS_LOCK("1110099","该店铺为锁定状态","该店铺为锁定状态"),
    SENSITIVE_WORD_EXIST("1110100","(%s)敏感词已存在","该敏感词已存在"),
    GOODS_NAME_CONTAIN_SENSITIVE_WORDS("1110101","商品名称存在敏感词(%s)","商品名称存在敏感词"),
    GOODS_NAME_CONTAIN_SENSITIVE_WORDS_COMMON("1110102","商品标题不合规，请参照平台相关政策","商品标题不合规，请参照平台相关政策"),
    GROUPON_PRICE_MUST_LESS_ORIGINAL_PRICE("1110200", "商品拼团价必须小于销售价", "商品拼团价必须小于销售价"),
    GROUPON_PRICE_IS_NULL("1110201", "商品拼团价不得为空", "商品拼团价不得为空"),
    SIZE_CHART_TEMPLATE_TYPE_WRONG("1110301","该尺码图模板(%s)不属于商家维护模板类型","该尺码图模板不属于商家维护模板类型"),
    SIZE_CHART_TEMPLATE_NOT_BELONG_SHOP("1110302","该尺码图模板(%s)不属于该商家","该尺码图模板不属于该商家"),
    SIZE_CHART_TEMPLATE_NOT_EXIST("1110303","该尺码图模板(%s)不存在","该尺码图模板不存在"),
    SIZE_CHART_TEMPLATE_CATEGORY_NOT_BIND_GOODS("1110304","绑定失败，该尺码图模板与商品类目不匹配","绑定失败，该尺码图模板与商品类目不匹配"),
    SIZE_CHART_TEMPLATE_CATEGORY_NOT_BIND_GOODS_EXT("1110304","绑定失败，该尺码图模板(%s)与商品(%s)类目不匹配","绑定失败，该尺码图模板与商品类目不匹配"),
    SIZE_CHART_TEMPLATE_MUST_BIND_SHOP_LAST_LEVEL_CATEGORY("1110305","绑定失败，请选择店铺所配置尺码表列表中的最下级类目的尺码表","绑定失败，请选择店铺所配置尺码表列表中的最下级类目的尺码表"),
    GOODS_SIGN_BID("1110401","商品(%s)已参加竞价，无法修改","商品已参加竞价，无法修改"),
    BRAND_NOT_EXIST("1110400","该商户品牌不存在，请检查后重新操作","该商户品牌不存在，请检查后重新操作"),
    BRAND_NOT_BELONG_SHOP("1110401","该品牌不属于该商户，请检查后重新操作","该品牌不属于该商户，请检查后重新操作"),
    BRAND_STORE_NOT_EXIST("1110402","该品牌库品牌不存在，请检查后重新操作","该品牌库品牌不存在，请检查后重新操作"),
    BRAND_IS_EXPIRE("1110403","该商户品牌授权已过期，请检查后重新操作","该商户品牌授权已过期，请检查后重新操作"),
    BRAND_STORE_IS_EXPIRE("1110404","品牌库信息已无效，请检查后重新操作","品牌库信息已无效，请检查后重新操作"),
    DOWNLOAD_ERROR("1110500", "文件下载失败", "文件下载失败"),
    FILE_SYNC_RECORD_NOT_FIND("1110501", "找不到导入记录", "找不到导入记录"),
    FILE_SYNC_RECORD_FINISHED("1110502", "该导入任务已完成", "该导入任务已完成"),
    FILE_SYNC_RECORD_DATA_ALL_FINISH("1110503", "找不到待处理的导入商品数据", "找不到待处理的导入商品数据"),

    GOODS_IS_AUDITING("1110305","更新失败，商品正在审核中","更新失败，商品正在审核中"),
    CONTAILS_CHINESE_ERROR("1110306", "商品标题、SKU规格、自定义属性、详情文字不能出现中文", "商品标题、SKU规格、自定义属性、详情文字不能出现中文"),
    CATEGORY_NOT_SAME("1110307", "只支持同一个末级类目的商品", "只支持同一个末级类目的商品"),
    GOODS_NOT_WAIT_AUDIT("1110308","商品(%s)未处于待确认或同盾驳回状态，无法去审核","商品未处于待确认或同盾驳回状态，无法去审核"),
    CONTAINS_CHINESE_ERROR_EXT("1110309", "商品标题、SKU规格、自定义属性、详情文字不能出现中文，检测到:%", "商品标题、SKU规格、自定义属性、详情文字不能出现中文"),
    GOODS_SHOW_STATUS_CAN_NOT_UPDATE("1110310", "商品当前在架状态不支持编辑", "商品当前在架状态不支持编辑"),

    EXCEL_DATA_PRAM_NULL("11000001","excel导入数据字段不完整，请检查后重新导入","excel导入数据字段不完整，请检查后重新导入"),
    EXCEL_READ_ERROR("11000008","excel读取错误","excel读取错误"),

    SHOP_ID_NULL("3000002","无法获取商家信息","无法获取商家信息"),
    EXCEL_DATA_EMPTY("3000003","excel导入文件解析的数据为空","excel导入文件解析的数据为空"),
    SKU_PROPERTY_FORBID_UPDATE("3000004","SKU规格信息禁止修改","SKU规格信息禁止修改"),
    SHOW_PRODUCT_STOCK_ERROR("3000006","上架商品必须有至少一个SKU在架且库存不为0","上架商品必须有至少一个SKU在架且库存不为0"),
    GOODS_SHOW_LOCKED("3000007","商品非下架状态，无法上架","商品非下架状态，无法上架"),
    GOODS_CANT_SHOW_FOR_NOT_OFF_SHELF("3000007","商品非下架状态，无法上架","商品非下架状态，无法上架"),
    GOODS_CANT_OFF_SHELF_FOR_NOT_ON_SHOW("3000007","商品非上架状态，无法下架","商品非上架状态，无法下架"),
    GOODS_SHOW_ERROR("3000008","商品上下架失败","商品上下架失败"),
    GOODS_PRICE_RATIO_ERROR("3000009","价差比例参数错误","价差比例参数错误"),
    GOODS_PRICE_DIFF_ERROR("3000010","价差差值参数错误","价差差值参数错误"),
    CATEGORY_PRICE_ERROR("3000011","该类目已经存在，请操作修改信息","该类目已经存在，请操作修改信息"),
    ADD_PRICE_ERROR("3000012","sku限价操作失败","sku限价操作失败"),
    PRICE_STATUS_ERROR("3000013","sku限价状态参数异常","sku限价状态参数异常"),
    PRICE_ID_ERROR("3000014","sku限价ID参数异常","sku限价ID参数异常"),
    SKU_LIMIT_PRICE_ERROR("3000015","sku价差过大，无法保存，如有疑问请联系商家运营","sku价差过大，无法保存，如有疑问请联系商家运营"),
    EXCEED_MAX_CATEGORY_PRICE("3000016","商品价格超过类目限价，请下调价格后再重新操作。如有疑问，请咨询店小二，谢谢","商品价格超过类目限价，请下调价格后再重新操作。如有疑问，请咨询店小二，谢谢"),
    EXCEL_UPDATE_CATEGORY_EXCEED("3000017","批量修改类目批次最大数量不能超过5000条","批量修改类目批次最大数量不能超过5000条"),

    PROPERTY_NAME_NOT_NULL("3001008","商品属性中英文名必传","商品属性中英文名必传"),
    PROPERTY_TYPE_ERROR("3001009","商品属性类型为空","商品属性类型为空"),
    PROPERTY_VALUE_ERROR("3001010","非法商品属性值","非法商品属性值"),
    PROPERTY_VALUE_ONLY_DEGIT("3001011","属性值非纯数字","属性值非纯数字"),
    PROPERTY_VALUE_ONLY_LETTER("3001012","属性值非纯字母","属性值非纯字母"),
    PROPERTY_VALUE_NOT_NULL("3001013","必填属性未维护，请维护后再上架","必填属性未维护，请维护后再上架"),
    PROPERTY_VALUE_NOT_NULL_EXT("3001013","必填属性(%s)未维护，请维护后再上架","必填属性未维护，请维护后再上架"),

    PROPERTY_CATEGORY_ID_REUSE("3000014","此类目已被绑定","此类目已被绑定"),
    PROPERTY_DETAIL_REUSE("3000015","属性重复:%s","属性重复"),
    PROPERTY_VALUE_ONLY_DEGIT_LETTER("3000016","属性值非数字或字母","属性值非数字或字母"),
    PROPERTY_VALUE_CONTAIN_INVALID_CHAR("3001017","商品属性(%s)值包含非法字符","商品属性值包含非法字符"),
    PROPERTY_VALUE_CHOICE_OPTIONS_IS_EMPTY("3001018","商品属性(%s)类型为单选/多选，且未配置可选列表，请联系管理员检查后再试","商品属性类型为单选/多选，且未配置可选列表，请联系管理员检查后再试"),
    PROPERTY_VALUE_CHOICE_VALUE_NOT_MATCH_OPTIONS("3001019","商品属性(%s)选择框所选值与可选值列表不匹配","商品属性选择框所选值与可选值列表不匹配"),
    PROPERTY_VALUE_TYPE_ERROR("3001020","商品属性(%s)类型错误，请联系管理员","商品属性类型错误，请联系管理员"),

    GOODS_IMPORT_PROPERTY_WRONG("3000101","商品导入失败-属性异常:%s","商品导入失败-属性异常"),
    GOODS_IMPORT_MAIN_IMAGE_WRONG("3000102","商品导入失败-主图异常:%s","商品导入失败-主图异常"),
    GOODS_IMPORT_DETAIL_IMAGE_WRONG("3000103","商品导入失败-轮播图/详情图异常:%s","商品导入失败-轮播图/详情图异常"),
    GOODS_IMPORT_DEFAULT_DELIVERY_WRONG("3000104","商品导入失败-默认运费异常:%s","商品导入失败-默认运费异常"),
    GOODS_IMPORT_WRONG("3000105","商品导入失败或部分失败:%s","商品导入失败或部分失败"),
    GOODS_IMPORT_PARAM_MISS("3000106","商品导入数据字段(%s)缺失","商品导入数据字段缺失"),
    GOODS_COUNT_LIMIT_WRONG("3000107","可添加商品数量已达到上线","可添加商品数量已达到上线"),
    GOODS_COUNT_LIMIT_EXCEED("3000108","导入商品数超过可添加商品剩余数量，请调整后重新导入","导入商品数超过可添加商品剩余数量，请调整后重新导入"),

    GOODS_QUERY_LIMIT_100("3000106","商品查询id输入不得大于100条","商品查询id输入不得大于100条"),
    GOODS_QUERY_LIMIT_200("3000107","上下架商品id不得大于200条","上下架商品id不得大于200条"),
    GOODS_PROPERTY_CANNOT_UPDATE("3000108","商品类目暂未配置属性模板，不支持修改属性","商品类目暂未配置属性模板，不支持修改属性"),
    CATEGORY_AVAILABLE_COUNTRY_EXIST("3000107","此类目当前已支持配置可售国家，请勿重复配置","此类目当前已支持配置可售国家，请勿重复配置"),
    PARAMETER_COUNTRY_ERROR("3000108", "country参数异常", "country参数异常"),
    WAREHOUSE_OVERSEA_JUST_SUPPORT_DIRECT_DELIVERY("3000109", "海外仓当前只支持直发店铺", "海外仓当前只支持直发店铺"),
    WAREHOUSE_OVERSEA_CONFIG_EXIST_COUNTRY("3000110", "当前店铺海外仓已存在该国家配置", "当前店铺海外仓已存在该国家配置"),
    WAREHOUSE_OVERSEA_CONFIG_NOT_FIND("3000111", "保存失败，找不到当前海外仓配置", "保存失败，找不到当前海外仓配置"),
    WAREHOUSE_OVERSEA_NOT_BELONG_COUNTRY("3000112", "存在店铺未配置的海外仓销售国家，请检查后重新操作", "存在店铺未配置的海外仓销售国家，请检查后重新操作"),
    WAREHOUSE_OVERSEA_MUST_CONFIG("3000113", "当前海外仓店铺未配置可售国家，请配置后再进行操作", "当前海外仓店铺未配置可售国家，请配置后再进行操作"),
    WAREHOUSE_OVERSEA_GOODS_FREIGHT_NEVER_REFRESH("3000114", "海外仓店铺商品无法自动刷新国家运费", "海外仓店铺商品无法自动刷新国家运费"),
    PROPERTY_DETAIL_NULL_REUSE("3000015","属性信息null","属性信息null"),
    PROPERTY_DETAIL_CATEGORY_REUSE("3000015","类目id参数异常","类目id参数异常"),
    GOODS_COUNT_INSUFFICIENT_ERROR("3000109", "店铺商品必须＞6个才能设置关联推荐商品", "店铺商品必须＞6个才能设置关联推荐商品"),
    RECOMMEND_EMPTY("3000110", "推荐主商品不存在或已删除", "推荐主商品不存在或已删除"),
    RECOMMEND_LIMIT("3000111", "推荐商品最多可关联9个", "推荐商品最多可关联9个"),

    ADD_LOCK_LABEL_LIMIT("3000200", "页面单次最多操作100个商品ID，如需大量操作可使用模板", "页面单次最多操作100个商品ID，如需大量操作可使用模板"),
    EXIST_LOCK_APPLY("3000201", "%s商品已存在审核中的解锁申请，请勿重新操作", "该商品已存在审核中的解锁申请，请勿重新操作"),
    GOODS_CANNOT_APPLY_LOCK("3000203", "%s商品不可申请解锁", "该商品不可申请解锁"),
    NOT_EXIST_UNLOCK_APPLY("3000204", "%s商品不存在解锁申请", "商品不存在解锁申请"),
    NOT_EXIST_LOCK_DATA("3000204", "%s商品不存在锁定数据", "商品不存在锁定数据"),

    BUY_ONE_PIECE_LIMIT("3000300", "您仅能提报一个商品，请先停止当前商品", "您仅能提报一个商品，请先停止当前商品"),
    BUY_ONE_PIECE_PRICE("3000301", "一口价必须≤销售价格的8折，请重新设置", "一口价必须≤销售价格的8折，请重新设置"),
    BUY_ONE_PIECE_STATUS_INCORRECT("3000302", "状态异常，无法进行该操作", "状态异常，无法进行该操作"),
    BUY_ONE_PIECE_PRICE_INCORRECT("3000303", "商品价格异常，请检查入参", "商品价格异常，请检查入参"),
    BUY_ONE_PIECE_GOODS_INVALID("3000304", "商品未审核通过，不可添加", "商品未审核通过，不可添加"),
    BUY_ONE_PIECE_ADD_ERROR("3000305", "添加商品失败，只能添加在架的", "添加商品失败，只能添加在架的"),
    BOTTOM_ONE_PIECE_ERROR("3000306", "同类目下相同商品只能添加一个", "同类目下相同商品只能添加一个"),

    CHANCE_GOODS_TEMPLATE_STATUS_FORBID_UPDATE("3000400", "当前模板非草稿状态，无法修改", "当前模板非草稿状态，无法修改"),
    CHANCE_GOODS_TEMPLATE_STATUS_FORBID_OPERATE("3000400", "当前模板非开放状态，无法操作", "当前模板非开放状态，无法操作"),
    CHANCE_GOODS_TEMPLATE_NOT_EXIST("3000401", "该模板不存在", "该模板不存在"),
    CHANCE_GOODS_TEMPLATE_NOT_EXIST_EXT("3000401", "该id模板(%s)不存在", "该id模板不存在"),
    GOODS_ITEM_NOT_EXIST("3000402", "该商品(%s)item不存在", "该商品item不存在"),
    PRODUCT_NOT_EXIST("3000403", "该产品(%s)不存在", "该产品不存在"),
    PRODUCT_SKU_NOT_EXIST("3000404", "该产品(%s)sku不存在", "该产品sku不存在"),
    GOODS_DETAIL_NOT_EXIST_EXT("3000405", "该商品(%s)详细信息不存在", "该商品详细信息不存在"),
    GOODS_EXT_DETAIL_NOT_EXIST("3000406", "该商品(%s)扩展详情信息不存在", "该商品扩展详情信息不存在"),
    GOODS_EXT_CATEGORY_NOT_EXIST("3000407", "该商品(%s)类目信息不存在", "该商品类目信息不存在"),
    GOODS_EXT_DETAIL_IMAGE_NOT_EXIST("3000408", "该商品(%s)详情图片信息不存在", "该商品详情图片信息不存在"),
    GOODS_IMAGE_NOT_EXIST("3000409", "该商品(%s)图片信息不存在", "该商品图片信息不存在"),
    CHANCE_GOODS_NOT_EXIST("3000410", "该机会商品不存在", "该机会商品不存在"),
    CHANCE_GOODS_NOT_EXIST_EXT("3000410", "该id机会商品(%s)不存在", "该机会商品不存在"),
    CHANCE_GOODS_CLONE_NEED_GOODS_ID("3000411", "机会商品模板克隆商品，商品id缺失", "机会商品模板克隆商品，商品id缺失"),
    CHANCE_GOODS_ALREADY_EXIST("3000412", "该商品(%s)已是机会商品，请勿重复添加", "该商品已是机会商品，请勿重复添加"),
    CHANCE_GOODS_TEMPLATE_ALREADY_CLONG_THE_GOODS("3000413", "已存在来源为该商品的模板，请勿重复添加", "已存在来源为该商品的模板，请勿重复添加"),
    CHANCE_GOODS_CANNOT_CLONG_TO_TEMPLATE("3000414", "该商品已是机会商品，请勿再克隆该商品为模板", "该商品已是机会商品，请勿再克隆该商品为模板"),

    CATEGORY_MAIN_NOT_EXIST("3000501", "该主营类目不存在", "该主营类目不存在"),
    CATEGORY_MAIN_NOT_INCLUDE("3000502", "该店铺主营类目不包含当前商品所属类目", "该店铺主营类目不包含当前商品所属类目"),
    SHOP_MUST_BIND_CATEGORY_MAIN("3000503", "该店铺未绑定主营类目", "该店铺未绑定主营类目"),
    GOODS_CATEGORY_MAIN_NOT_INCLUDE("3000504", "该商品(%s)所属店铺主营类目不包含当前类目", "该商品所属店铺主营类目不包含当前类目"),

    PRICE_REDUCTION_GOODS_INVALID("3000151", "商品状态非上架中，不可参与建议降价", "商品状态非上架中，不可参与建议降价"),
    PRICE_REDUCTION_GOODS_LIMIT("3000152", "每次最多导入500条", "每次最多导入500条"),
    PRICE_REDUCTION_GOODS_FREQUENCY("3000153", "30秒内请勿重复导入同一批商品", "30秒内请勿重复导入同一批商品"),
    UPGRADE_TO_LATEST_VERSION("0100","Please upgrade Voghion to the latest version","请升级至最新版本"),
    GOODS_FAVORITE("3000154", "%s商品已被该收藏夹收藏", "商品已被该收藏夹收藏"),
    SHOP_ERROR("900017", "店铺状态异常", "店铺状态异常"),

    BRAND_NULL_ERROR("3000160","商品品牌必填","商品品牌必填"),
    SYSTEM_SHOP_ERROR("3000161","由于平台在严厉打击侵权行为清理商品，下架商品暂时无法上架，预计2月22日清理完成后放开上架","由于平台在严厉打击侵权行为清理商品，下架商品暂时无法上架，预计2月22日清理完成后放开上架"),

    GOODS_STANDARD_EXIST("3000170","该类目商品规范已存在","该类目商品规范已存在"),
    GOODS_STANDARD_NOT_EXIST("3000171","该商品规范不存在","该商品规范不存在"),
    NOT_ENABLE_CUSTOMIZE_MUST_HAVE_DEFAULT_PROPERTY("3000171","不允许规格自定义添加，必须配置默认规格","不允许规格自定义添加，必须配置默认规格"),
    PROPERTY_VALUE_OPTION_CANNOT_NULL("3000172","可选规格名不得为空","可选规格名不得为空"),
    PROPERTY_NAME_OPTION_CANNOT_REPEAT("3000173","规格名称不得重复","规格名称不得重复"),
    PROPERTY_VALUE_OPTION_CANNOT_REPEAT("3000174","可选规格值不得重复","可选规格值不得重复"),
    CANNOT_CHOOSE_NO_BRAND("3000175","商品规范校验:品牌不可选no Brand","商品规范校验: 品牌不可选no Brand"),
    CANNOT_SETTING_FREIGHT("3000176","商品规范校验:不允许配置国家运费","商品规范校验:不允许配置国家运费"),
    PROPERTY_NAME_MUST_BE_DEFAULT_STANDARD("3000177","商品规范校验:必须指定默认规格","商品规范校验:必须指定默认规格"),
    PROPERTY_CANNOT_SET_IMAGE("3000178","商品规范校验:指定规格不允许配图","商品规范校验:指定规格不允许配图"),
    PROPERTY_MUST_SET_DEFAULT_VALUE("3000179","商品规范校验:指定规格必须使用默认规格值","商品规范校验:指定规格必须使用默认规格值"),
    GOODS_FREIGHT_PRICE_NULL("3000190","国家运费金额不得为空","国家运费金额不得为空"),
    SKU_PROPERTY_NOT_MATCH_IN_PROPERTIES("3000191","存在sku规格值与规格集信息不匹配","存在sku规格值与规格集信息不匹配"),

    SPIDER_ALIEXPRESS_PARAM_NULL("3000192","速卖通爬取传入的参数为空","速卖通爬取传入的参数为空"),
    SPIDER_IMAGE_NULL("3000193","爬虫数据的图片信息缺失","爬虫数据的图片信息缺失"),
    SPIDER_PROPERTY_NULL("3000194","爬虫数据的规格信息缺失","爬虫数据的规格信息缺失"),

    BUYER_SET_IS_EMPTY("3000399","配置买手列表为空","配置买手列表为空"),
    HOT_SAME_GOODS_NOT_EXIST("3000300","该热卖商品此同款商品不存在","该热卖商品此同款商品不存在"),
    USER_NOT_BUYER("3000400","当前用户非买手，不可操作","当前用户非买手，不可操作"),
    GOODS_NOT_BELONG_BUYER_SHOP("3000401","存在不属于该买手用户负责店铺的商品，请检查后重新操作","存在不属于该买手用户负责店铺的商品，请检查后重新操作"),
    BUYER_NOT_MATCH_SHOP("3000402","该买手查无关联店铺","该买手查无关联店铺"),
    BUYER_TOKEN_WRONG("3000403","该买手token无效","该买手token无效"),
    ARRIVAL_SET_IS_EMPTY("3000404","配置上新人列表为空","配置上新人列表为空"),
    USER_NOT_ARRIVAL("3000405","该用户(%s)非上新人，请检查后重试","该用户(%s)非上新人，请检查后重试"),
    GOODS_NOT_BELONG_ARRIVAL("3000406","该商品(%s)不在当前上新人的负责范围","该商品(%s)不在当前上新人的负责范围"),

    LISTING_GOODS_ALREADY_EXIST("3000500", "该商品(%s)已是listing商品，请勿重复添加", "该商品已是listing商品，请勿重复添加"),
    LISTING_NOT_EXIST("3000501", "该listing商品不存在或已删除", "该listing商品不存在或已删除"),
    LISTING_STATUS_CAN_NOT_SIGN("3000502", "该listing商品当前状态不支持竞标", "该listing商品当前状态不支持竞标"),
    LISTING_ALREADY_SIGN_UP("3000503", "您已经参与了该商品竞标，请在商品管理中查看", "您已经参与了该商品竞标，请在商品管理中查看"),
    LISTING_PRICE_EXCEED_TEMPLATE("3000504", "价格高于listing推荐价格，请输入不高于推荐价格以获取流量支持", "价格高于listing推荐价格，请输入不高于推荐价格以获取流量支持"),
    LISTING_GOODS_FORBID_UPDATE("3000505", "listing商品暂不支持修改", "listing商品暂不支持修改"),
    LISTING_CANNOT_FROM_WHOLESALE("3000506", "无法以批发商品为来源创建listing模板", "无法以批发商品为来源创建listing模板"),
    LISTING_CANNOT_DELETE_FOR_EXIST_OTHER_FOLLOW("3000507", "以下listingId: (%s)，listing模板存在其他跟卖商品，无法删除，其余listing模板已成功删除",
            "以下listingId: (%s)，listing模板存在其他跟卖商品，无法删除，其余listing模板已成功删除"),
    LISTING_RELATION_NUM_EXCEED_MAX_ERROR("3000508", "listingId或商家Id的个数不能超过500", "listingId或商家Id的个数不能超过500"),
    LISTING_CONFIG_UPDATE_ERROR("3000509", "商品类型为负向时，至少保留一个A类店铺或者B类店铺或C类店铺的标签", "商品类型为负向时，至少保留一个A类店铺或者B类店铺的标签"),
    LISTING_CONFIG_BATCH_UPDATE_ERROR("3000510", "部分listing id：(%s)修改失败，原因：当前商品类型为负向，删除标签时，至少保留一个A类店铺或B类店铺或C类店铺的标签。其余listing id已修改成功",
            "部分listing id：(%s)修改失败，原因：当前商品类型为负向，删除标签时，至少保留一个A类店铺或B类店铺或C类店铺的标签。其余listing id已修改成功"),

    GOODS_SKU_PROPERTY_FORBID_ADD("3000600","该商品规格信息不允许新增","该商品规格信息不允许新增"),
    GOODS_SKU_PROPERTY_FORBID_DEL("3000601","该商品规格信息不允许删除","该商品规格信息不允许删除"),
    PROPERTY_NAME_REPEAT("3000602","规格名称(%s)重复","规格名称重复"),
    PROPERTY_VALUE_REPEAT("3000603","规格值(%s)重复","规格值重复"),
    PROPERTY_NAME_EMPTY("3000604","规格名称不得为空","规格名称不得为空"),
    PROPERTY_VALUE_EMPTY("3000605","规格值不得为空","规格值不得为空"),
    PROPERTY_EMPTY("3000606","规格信息不得为空","规格信息不得为空"),
    PROPERTY_VALUE_TOO_LONG("3000607","规格值字符过长","规格值字符过长"),

    GOODS_EDIT_APPLY_EXIST("3000700", "已有修改申请在审核中，请至调整历史查看页撤销申请", "已有修改申请在审核中，请至调整历史查看页撤销申请"),
    GOODS_EDIT_APPLY_EXIST_EXT("30007000", "已有修改申请(%s)在审核中，请至调整历史查看页撤销申请", "已有修改申请在审核中，请至调整历史查看页撤销申请"),
    GOODS_EDIT_APPLY_NOT_EXIST("3000701", "该商品修改价格库存申请不存在", "该商品修改价格库存申请不存在"),
    GOODS_EDIT_APPLY_ALREADY_AUDIT("3000702", "该商品修改申请非待审批状态", "该商品修改申请非待审批状态"),
    GOODS_EDIT_CHANGE_NOT_FIND("3000703", "不存在改动内容，无需保存", "不存在改动内容，无需保存"),
    GOODS_EDIT_LISTING_UNAUTHORIZED("3000704", "listing商品审核无权处理", "listing商品审核无权处理"),
    GOODS_EDIT_DETAIL_NOT_EXIST("3000705", "该商品修改历史详情不存在", "该商品修改历史详情不存在"),

    CATEGORY_NOT_EXIST_SYSTEM_SIZE_CHART_TEMPLATE("3000800","该类目不存在尺码表规范","该类目不存在尺码表规范"),

    //商户报错用400 开头
    SHOP_IS_NOT_EXIST("4000001","该商户不存在","商户不存在"),
    GOODS_ID_LIST_EXIST("4000002","商品id列表存在，存在的商品id列表为%s","商品id列表存在"),

    HAS_DISABLE_EXIST("4000003","商家id为：%s，正在禁售中，请勿重复操作","正在禁售中，请勿重复操作"),
    HAS_LEAVE_EXIST("4000004","商家id为：%s，正在休假中，请勿操作","正在休假中，请勿操作"),
    OPT_ERROR("4000005","操作失败，请稍后重试","操作失败，请稍后重试"),
    OPT_SHOP_LIMIT("4000006", "单次操作商家上限100个，请控制在100个内再操作", "单次操作商家上限100个，请控制在100个内再操作"),
    NOT_IN_DISABLE("4000007", "商家不在禁用中，请确认后重试", "商家不在禁用中，请确认后重试"),
    NOT_IN_DISABLE_SHOP("4000008", "商家id为：%s，不在禁用中，请确认后重试", "商家不在禁用中，请确认后重试"),
    HAS_LIMITING_EXIST("4000009","商家id为：%s，正在限流中，请勿重复操作","正在限流中，请勿重复操作"),
    NOT_IN_LIMITING("4000010", "商家不在限流中，请确认后重试", "商家不在限流中，请确认后重试"),
    NOT_IN_LIMITING_SHOP("4000011", "商家id为：%s，不在限流中，请确认后重试", "商家不在限流中，请确认后重试"),

    PROPERTY_TEMPLATE_LIMIT("3000180","同类目最多支持维护20个模板","同类目最多支持维护20个模板"),
    PERMISSIONS_DENIED("3000181","无权限修改","无权限修改"),

    GOODS_VAT_NOT_LEGAL("3000182","商品vat范围不合法","商品vat范围不合法"),

    PROPERTY_CONTAILS_CHINESE_ERROR("3000183", "属性中不能出现中文", "属性中不能出现中文"),
    PROPERTY_EN_NAME_CONTAILS_CHINESE_ERROR("3000184", "属性英文名中不能出现中文", "属性英文名中不能出现中文"),
    GOODS_LIST_ID_NUM_TO_MUCH("3000185","商品id数量超过查询上限，请分段查询","商品id数量超过查询上限，请分段查询"),
    GOODS_LIST_ID_NUM_TO_MANY("3000186","商品id数量过万,请分段查询","商品id数量过万,请分段查询"),
    GOODS_ID_NUM_PASS_THIRTY_THOUSAND("3000187","商品id数量超过三万上限,请检查后再试","商品id数量超过三万上限,请检查后再试"),


    WX_MOBILE_NULL("3000200","手机号不能为空","手机号不能为空"),
    WX_OPENID_NULL("3000201","微信openId不能为空","微信openId不能为空"),
    WX_CAPTCHA_NULL("3000202","验证码不能为空","验证码不能为空"),
    WX_CAPTCHA_CHECK_ERROR("3000203","验证码校验失败","验证码校验失败"),
    WX_MOBILE_BIND_ERROR("3000204","该手机号未检测到绑定店铺，暂时无法使用该功能","该手机号未检测到绑定店铺，暂时无法使用该功能"),
    WX_UNBIND_NULL("3000205","微信暂未绑定后台账号,请先去后台绑定，否则无法使用该功能","微信暂未绑定后台账号,请先去后台绑定，否则无法使用该功能"),
    WX_BIND_SUCCESS("3000206","已绑定公众号,请勿重复绑定","已绑定公众号,请勿重复绑定"),
    LISTING_SHOP_WHITE_EXIST("3000207", "该listing白名单商家(%s)已存在", "该listing白名单商家已存在"),
    SHOP_NOT_EXIST("3000208", "商家(%s)不存在", "不存在"),
    LISTING_ADD_GOODS_ERROR("3000209", "该商品(%s)添加listing商品失败", "添加失败"),
    LISTING_ADD_GOODS_HOT_ERROR("3000210", "该listing(%s)修改热度失败", "添加失败"),
    NOT_IN_WHITE("3000211", "商家无法竞选", "商家无法竞选"),

    RUN_DAYS_NOT_NULL("3000212", "请选择天数", "请选择天数"),
    SORT_ITEM_NOT_NULL("3000213", "排序字段不能为空", "排序字段不能为空"),
    EQUAL_TASK_NAME("3000214", "存在相同任务名", "存在相同任务名"),
    EQUAL_CUSTOM_ID("3000215", "虚拟列表id已被绑定", "虚拟列表id已被绑定"),
    CUSTOM_ID_NOT_NULL("3000216", "虚拟列表id不能为空", "虚拟列表id已被绑定"),
    MIN_TOTAL_NOT_NULL("3000217", "最低条数不能为空", "最低条数不能为空"),
    WAREHOUSE_STOCK_ERROR("3000218", "备货仓商品不允许修改规格", "备货仓商品不允许修改规格"),
    GOODS_SIZE_MAX_ERROR("3000219", "商品数为空或大于3W请重新操作", "商品数为空或大于3W请重新操作"),
    CUSTOM_ID_NULL_ERROR("3000220", "虚拟列表id不能为空", "虚拟列表id不能为空"),

    GOODS_FREIGHT_GRADIENT_WEIGHT_EXCEED("3000800","单件批发商品重量不得超过950g","单件批发商品重量不得超过950g"),
    SKU_STATUS_NULL("3000801","sku状态不能为空","sku状态不能为空"),
    WEIGHT_MUST_NOT_ZERO("3000802","商品重量不允许设置为0kg","商品重量不允许设置为0kg"),
    GOODS_LOGISTICS_NOT_EXIST("3000803","该id物流属性数据不存存在","该id物流属性数据不存存在"),
    CATEGORY_GOODS_LOGISTICS_ALREADY_EXIST("3000804","该类目的物流属性数据已存在","该类目的物流属性数据已存在"),
    LOGISTICS_PROPERTY_ID_IS_WRONG("3000805","物流属性id不合规","物流属性id不合规"),
    EXCEL_MAX_1000("3000806","excel导入数量最大不得超过1000条","excel导入数量最大不得超过1000条"),
    WEIGHT_MUST_LESS_2KG("3000807","商品重量必须小于2kg，若有特殊情况请联系管理员","商品重量必须小于2kg，若有特殊情况请联系管理员"),
    CATEGORY_LOGISTICS_PROPERTY_NOT_MATCH("3000808","该物流属性与类目不匹配，当前可选:%s","该物流属性与类目不匹配"),
    COUNTRY_FREIGHT_NOT_HIT("3000809","未命中物流价卡","未命中物流价卡"),
    FLASHDEAL_CANNOT_UPDATE_PRICE("3000810","flash商品不允许改价格重量","flash商品不允许改价格重量"),
    SPIDER_1688_NOT_HIT_PRICE_MODEL("3000811","1688爬取商品未命中价格模型","1688爬取商品未命中价格模型"),
    ALI_CALL_FAIL("3000812","调用1688接口失败","调用1688接口失败"),
    LARGE_LOGISTICS_PROPERTY_ERROR("3000813","当前商品不可选大件物流属性","当前商品不可选大件物流属性"),
    GOODS_TAG_NOT_EXIST("3000819","商品打标标签ID无效，请联系管理员","商品打标标签ID无效，请联系管理员"),
    COMPLIANCE_LABEL_TAG_CONFIG_EXIST("3000820","该合规打标驳回标签已存在","该合规打标驳回标签已存在"),
    COMPLIANCE_LABEL_TAG_CONFIG_NOT_EXIST("3000821","该合规打标驳回标签不存在","该合规打标驳回标签不存在"),
    AUDIT_TASK_AUDITOR_EXIST("3000822","该审核人已存在","该审核人已存在"),
    AUDIT_TASK_AUDITOR_NOT_EXIST("3000823","该审核人不存在","该审核人不存在"),
    AUDIT_TASK_TYPE_INVALID("3000824","非法任务类型","非法任务类型"),
    AUDIT_TASK_NOT_FIND("3000825","查不到该审核任务","查不到该审核任务"),
    AUDIT_TASK_TYPE_DIFFERENT("3000826","无法同时分配不同类型的审核任务","无法同时分配不同类型的审核任务"),
    AUDIT_TASK_BATCH_LIMIT("3000827","新建任务单次最多10000个商品","新建任务单次最多10000个商品"),
    AUDIT_TASK_STATUS_WRONG("3000828","当前商品(%s)审核任务状态不可操作","当前商品审核任务状态不可操作"),
    AUDIT_TASK_AUDITOR_PERMISSION_WRONG("3000830","未分配到该审核任务，无法操作","未分配到该审核任务，无法操作"),
    AUDIT_TASK_FIELD_TYPE_NOT_NULL("3000831","审核任务的业务类型type不得为空","审核任务的业务类型type不得为空"),
    CATEGORY_LEVEL_MUST_FIRST("3000832","类目必须为一级类目","类目必须为一级类目"),

    GOODS_LOGISTICS_CONFIG_NOT_EXIST("3000804","商品物流属性不存在或已失效！！！请配置！！！","商品物流属性不存在或已失效！！！请配置！！！"),
    LISTING_PRODUCT_MAINTENANCE("3000833","竞标商品维护中暂时不允许参与竞标","竞标商品维护中暂时不允许参与竞标"),
    AUDIT_TASK_STATUS_ERROR("3000834","非同盾审核审核驳回或禁售状态,不能操作","非同盾审核审核驳回或禁售状态,不能操作"),
    APPEAL_REASON_IS_NULL("3000836","商家申述原因不能为空","商家申述原因不能为空"),
    APPEAL_CONTENT_IS_NULL("3000835","商家申述内容不能为空","商家申述内容不能为空"),
    GOODS_ALREADY_APPEAL("3000836","该商品已申述,不能操作","该商品已申述,不能操作"),
    GOODS_APPEAL_IS_NULL("3000837","该商品商家申述信息为空","该商品商家申述信息为空"),
    APPEAL_AUDIT_STATUS_ERROR("3000838","该状态不能撤销申述","该状态不能撤销申述"),


    SHOP_DO_NOT_HAVA_US_AGGREMENT("3000834","请先签署协议后再新增美国可售","请先签署协议后再新增美国可售"),
    OPERATION_UPDATE_COUNTRY_ERROR("3000835","运营平台只有1688自营店铺才能修改国家！！","运营平台只有1688自营店铺才能修改国家！！"),
    NOT_HIT_ALIEXPRESS_CATEGORY_SHOP_MAPPING("3000836","查询不到速卖通商品导入对应类目的店铺","查询不到速卖通商品导入对应类目的店铺"),

    GOODS_MUST_BE_WHOLESALE("3000840","商品必须为批发商品","商品必须为批发商品"),
    GOODS_ALREADY_INIT_SKU_GRADIENT_PRICE("3000841","该商品已刷新过sku梯度价格","该商品已刷新过sku梯度价格"),
    GOODS_MISS_COST_PRICE("3000842","该商品没有采购成本，无法计算批发价","该商品没有采购成本，无法计算批发价"),
    WHOLESALE_GOODS_CANNOT_UPDATE("3000843","批发商品暂不支持修改，如需修改请联系产品","批发商品暂不支持修改，如需修改请联系产品"),
    NOT_FIND_GOODS_ITEM_BY_SKU_ID("3000844","根据skuId查不到对应sku信息","根据skuId查不到对应sku信息"),
    CUSTOM_NOT_CATEGORY("3000845","不存在导入的类目限制","不存在导入的类目限制"),
    TRIGGER_UPDATE_WHOLESALE_PRICE_WRONG_BY_NOT_NIHAO("3000846","手动更新批发商品价格(%s)，无GoodsMove信息且非nihao商品，不更新","手动更新批发商品价格，无GoodsMove信息且非nihao商品，不更新"),
    PARAM_COUNTRY_ERROR("3000847","国家country参数异常！！!","国家country参数异常！！!"),
    MIN_PURCHASE_QUANTITY_MAX_FIVE("3000848","商品最小购买件数，最大只能设置为5件起买","商品最小购买件数，最大只能设置为5件起买"),
    SIZE_CHART_DATA_MUST_DIFFERENT("3000849","请准确填写尺码表，不允许所有size填写同一个数值","请准确填写尺码表，不允许所有size填写同一个数值"),
    OVER_WEIGHT_MUST_BE_LARGE_LOGISTICS("3000850","商品超重，请选择大件普货/大件含电物流属性，如果无法选择请联系小二反馈","商品超重，请选择大件普货/大件含电物流属性，如果无法选择请联系小二反馈"),
    COST_URL_EXIST_SELF_SHOP("3000851","此链接已被%s店铺搬运过","此链接已被XXXXX店铺搬运过"),


    AUDIT_RULE_NOT_EXIST("3001000","该审核规则不存在","该审核规则不存在"),
    AUDIT_RULE_TASK_TYPE_NOT_NULL("3001001","自动审核规则适用业务类型不得为空","自动审核规则适用业务类型不得为空"),
    AUDIT_RULE_REJECT_ID_NOT_NULL("3001002","自动审核规则驳回id不得为空","自动审核规则驳回id不得为空"),
    AUDIT_RULE_REJECT_REASON_NOT_NULL("3001003","自动审核规则驳回原因不得为空","自动审核规则驳回原因不得为空"),
    AUDIT_RULE_CATEGORY_ID_NOT_NULL("3001004","自动审核规则类目id不得为空","自动审核规则类目id不得为空"),
    AUDIT_RULE_CATEGORY_NAME_NOT_NULL("3001005","自动审核规则类目名称不得为空","自动审核规则类目名称不得为空"),
    AUDIT_RULE_EXIST("3001006","自动审核规则该类目配置已存在","自动审核规则该类目配置已存在"),
    GUOQING_ERROR("3001007","营销推广中商品(%s)不允许下架","营销推广中商品(%s)不允许下架"),
    AUDIT_RULE_TASK_STATUS_NOT_NULL("3001008","自动审核规则状态不能为空","自动审核规则状态不能为空"),
    AUDIT_RULE_TASK_AUDIT_RESULT_NOT_NULL("3001009","自动审核规则审核结果不能为空","自动审核规则审核结果不能为空"),

    PROPERTY_NUM_EXCEED_MAX_ERROR("3001014", "自定义属性最多创建5个", "自定义属性最多创建5个"),

    PROPERTY_NAME_LEN_EXCEED_MAX_ERROR("3001015", "自定义属性名最多20个字符", "自定义属性名最多20个字符"),

    PROPERTY_VALUE_LEN_EXCEED_MAX_ERROR("3001016", "自定义属性值最多60个字符", "自定义属性值最多60个字符"),

    PROPERTY_NAME_OR_VALUE_ONLY_DIGIT_ERROR("3001021","属性名和属性值都不能为纯数字","属性名和属性值都不能为纯数字"),

    PROBLEM_GOODS_PUSH_EXIST("3001100","该商品(%s)已存在问题push任务，请在原来任务上操作重新处理","该商品已存在问题push任务，请在原来任务上操作重新处理"),
    PROBLEM_GOODS_PUSH_NOT_EXIST("3001101","该商品(%s)不存在问题push任务","该商品不存在问题push任务"),
    PROBLEM_GOODS_STATUS_NOT_WAIT_HANDLE("3001102","该商品(%s)问题push任务状态已关闭或已完成，无法关闭","该商品问题任务状态已关闭或已完成，无法关闭"),
    PROBLEM_GOODS_STATUS_NOT_HANDLED("3001103","该商品(%s)问题push任务状态非商家已处理，无法确认完成","该商品问题push任务状态非商家已处理，无法确认完成"),
    EXCEL_IMAGE_ERROR("3001104","excel文件图片格式错误:第%s行，请更换或删除","excel文件图片格式错误，请更换或删除"),
    EXCEL_FIELD_EMPTY("3001105","excel文件必填字段缺失:第%s行，请检查修改后重试","excel文件必填字段缺失，请检查修改后重试"),
    SYNC_FAIL("3001106","同步系统改造中暂不可使用","同步系统改造中暂不可使用"),

    PARAM_DIMENSION_ERROR("3001107","参数DIMENSION异常！！","参数DIMENSION异常！！"),
    PARAM_GOODS_REAL_SHOT_CONF_ID_ERROR("3001108","参数实拍图配置id异常！！","参数实拍图配置id异常！！"),
    GOODS_REAL_SHOT_CONF_EXIST("3001109", "该实拍图配置已存在！！", "该实拍图配置已存在！！"),
    GOODS_REAL_SHOT_CONF_ERROR("3001109", "该实拍图配置不存在！！", "该实拍图配置不存在！！"),
    SHOP_INFO_ERROR("3001110", "店铺ID: %s 对应的店铺信息不存在！！", "店铺ID: %s 对应的店铺信息不存在！！"),
    CATEGORY_INFO_ERROR("3001111", "类目ID: %s 对应的类目信息不存在！！", "类目ID: %s 对应的类目信息不存在！！"),

    FILTER_WORDS_NOT_EXIST("3002000","该拦截词不存在","该拦截词不存在"),
    FILTER_WORDS_NAME_NOT_NULL("3002001","拦截词名称不能为空","拦截词名称不能为空"),
    FILTER_LABEL_CATEGORY_NOT_EXIST("3002002","该拦截词标签不存在","该拦截词标签不存在"),
    FILTER_LABEL_NOT_LEVEL_TWO("3002003","该拦截词标签非二级标签","该拦截词标签非二级标签"),
    FILTER_WORDS_NOT_CATEGORY_NOT_NULL("3002004","拦截词生效类目配置不能为空","拦截词生效类目配置不能为空"),
    FILTER_WORDS_EXIST("3002005","该拦截词配置已存在","该拦截词配置已存在"),
    FILTER_RECORD_NOT_EXIST("3002006","该拦截词命中记录不存在","该拦截词命中记录不存在"),
    FILTER_WORDS_NAME_IS_EXIST("3002007","拦截词名称不能重复","拦截词名称不能重复"),
    IS_REFRESH_SECOND_CONFIG_IS_NULL("3002008","是否刷新所属副词设置不能为空","是否刷新所属副词设置不能为空"),
    FILTER_REL_MAIN_WORDS_NOT_EXIST("3002009","该拦截词关联主词不存在","该拦截词关联主词不存在"),
    FILTER_WORDS_CONFIG_NOT_REPEAT("3002010","拦截主副词不能配置相同","拦截主副词不能配置相同"),
    FILTER_WORDS_MAIN_WORDS_NOT_EXIST("3002011","拦截主词不存在","拦截主词不存在"),

    ADD_REAL_SHOT_IMAGE_ERROR("3001113", "添加商品实拍图异常", "添加商品实拍图异常"),
    UPDATE_REAL_SHOT_IMAGE_ERROR("3001114", "更新商品实拍图异常", "更新商品实拍图异常"),
    PARAM_GOODS_REAL_SHOT_CONF_IS_NULL("3001115","实拍图配置店铺标签、国家、类目三者不能都为空","实拍图配置店铺标签、国家、类目三者不能都为空"),
    PARAM_CONF_STATUS_IS_NULL("3001116","实拍图配置状态不能为空","实拍图配置状态不能为空"),
    PARAM_CONF_CATEGORY_IS_ERROR("3001117","实拍图配置类目异常,请检查后重试","实拍图配置类目异常,请检查后重试"),

    GOODS_IS_FREEZE("3001107","更新失败，商品正在冻结中","更新失败，商品正在冻结中"),
    GOODS_IS_FREEZE_EXT("3001108", "%s商品冻结中", "商品冻结中"),
    SKU_PICTURE_EMPTY("3001109", "存在sku的图片为空", "存在sku的图片为空"),
    COUNTRY_NOT_SUP("3001110", "不存在支持可售国家", "不存在支持可售国家"),


    EXIST_GOODS_NOT_OPERATE_TYPE("3001110","%s商品没有表明操作类型！！","商品没有表明操作类型！！"),

    GOODS_COUNTRY_MANUAL_REQUIRED("3001111","此类目商品必须上传英文版国家说明书","此类目商品必须上传英文版国家说明书"),

    GOODS_AVAILABLE_COUNTRY_EMPTY("3001112","商品 %s 可售国家为空","商品可售国家为空"),
    GOODS_PARTS_EMPTY("3001113","车辆适配信息不能为空","车辆适配信息不能为空"),

    CUSTOM_EXPORT_ERROR("3001113","虚拟列表%s导出异常","虚拟列导出异常"),
    CUSTOM_EXPORT_ING("3001114","虚拟列表%s正在导出","虚拟列表正在导出"),

    BLACK_SAMPLE_AUDIT_TASK_STATUS_ERROR("3003000","审核任务状态错误","审核任务状态错误"),
    BLACK_SAMPLE_AUDIT_TASK_AUDITOR_ERROR("3003001","不允许审核他人任务","不允许审核他人任务"),
    ;

    /**
     * 结果码
     */
    private final String code;
    /**
     * 返回消息
     */
    private String msg;
    /**
     * 描述
     */
    private final String detail;

     ProductResultCode(String code, String msg, String detail) {
        this.code = code;
        this.msg = msg;
        this.detail = detail;
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getDetail() {
        return detail;
    }

    @Override
    public String getDetail(String[] arg0) {
        return null;
    }

    @Override
    public String getMsg() {
        return msg;
    }

    private void setMsg(String msg) {
         this.msg = msg;
    }

    @Override
    public String getName() {
        return name();
    }

    public static ProductResultCode getEnumByCode(String code) {
        for(ProductResultCode p : ProductResultCode.values()) {
            if(p.getCode().equalsIgnoreCase(code)) {
                return p;
            }
        }
        return null;
    }
    

    @Override
    public String toString() {
        JSONObject object = new JSONObject();
        object.put("code", code);
        object.put("msg", msg);
        return JSON.toJSONString(object);
    }

}
