package com.voghion.product.model.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;


@Data
public class SnapchatDataFeedDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ExcelProperty(value = "id", index = 0)
    private Long id;

    @ExcelProperty(value = "title", index = 1)
    private String title;

    @ExcelProperty(value = "description", index = 2)
    private String description;

    @ExcelProperty(value = "link", index = 3)
    private String link;

    @ExcelProperty(value = "image_link", index = 4)
    private String imageLink;

    @ExcelProperty(value = "availability", index = 5)
    private String availability;

    @ExcelProperty(value = "price", index = 6)
    private String price;

    @ExcelProperty(value = "brand", index = 7)
    private String brand;

    @ExcelProperty(value = "icon_media_url", index = 8)
    private String iconMediaUrl;

    @ExcelProperty(value = "ios_app_name", index = 9)
    private String iosAppName;

    @ExcelProperty(value = "ios_app_store_id", index = 10)
    private String iosAppStoreId;

    @ExcelProperty(value = "ios_url", index = 11)
    private String iosUrl;

    @ExcelProperty(value = "android_app_name", index = 12)
    private String androidAppName;

    @ExcelProperty(value = "android_package", index = 13)
    private String androidPackage;

    @ExcelProperty(value = "android_url", index = 14)
    private String androidUrl;

    @ExcelProperty(value = "mobile_link", index = 15)
    private String mobileLink;

    @ExcelProperty(value = "condition", index = 16)
    private String condition;

    @ExcelProperty(value = "custom_label_0", index = 17)
    private String customLabel0;

    @ExcelProperty(value = "custom_label_1", index = 18)
    private String customLabel1;

    @ExcelProperty(value = "custom_label_2", index = 19)
    private String customLabel2;

    @ExcelProperty(value = "custom_label_3", index = 20)
    private String customLabel3;

    @ExcelProperty(value = "custom_label_4", index = 21)
    private String customLabel4;

    @ExcelProperty(value = "sale_price", index = 22)
    private String salePrice;

    @ExcelProperty(value = "product_type", index = 24)
    private String productType;

    @ExcelProperty(value = "gender", index = 25)
    private String gender;
}
