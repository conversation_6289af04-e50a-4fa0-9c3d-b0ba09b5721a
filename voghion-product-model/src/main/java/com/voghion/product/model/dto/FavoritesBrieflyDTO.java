package com.voghion.product.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel
public class FavoritesBrieflyDTO {
    /**
     * 自增id
     */
    @ApiModelProperty(value = "活动id")
    private Long id;

    /**
     * 商品收藏夹名称
     */
    @ApiModelProperty(value = "商品收藏夹名称")
    private String favoritesName;

    /**
     * 创建人名称
     */
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 创建人id
     */
    @ApiModelProperty(value = "创建人id")
    private Long createUserId;
}
