package com.voghion.product.model.dto;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 商家属性模板表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-06
 */
@Data
public class PropertyTemplateDto implements Serializable{
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 类目id
     */
    private Long categoryId;

    /**
     * 类目名称
     */
    private String categoryName;

    /**
     * 模板名称
     */
    private String name;

    /**
     * 店铺id
     */
    private Long shopId;

    /**
     * 创建人
     */
    private String createName;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 最后修改人
     */
    private String updateName;

    /**
     * 最后修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 自定义属性json串
     */
    private String propertyJson;

}
