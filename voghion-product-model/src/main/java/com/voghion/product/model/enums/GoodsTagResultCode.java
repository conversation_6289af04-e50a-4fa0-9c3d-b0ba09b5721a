package com.voghion.product.model.enums;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.colorlight.base.model.BaseResultCode;
import lombok.Getter;

/**
 * GoodsTagResultCode
 *
 * <AUTHOR>
 * @date 2022/12/09
 */
@Getter
public enum GoodsTagResultCode implements BaseResultCode {
    /**
     * 0开头的表示正常
     * 1开头的表示系统异常
     * 11表示商品异常
     * 110表示参数异常
     * 111表示系统异常
     * <p>
     * <p>
     * 2开头的表示参数异常|201开头的表示系统通用参数
     */

    SUCCESS("0000", "SUCCESS", "成功"),
    PARAM_ERROR("100000", "打标签参数异常", "打标签参数异常"),

    TAG_NAME_NULL_ERROR("100001", "标签名称不能为空", "标签名称不能为空"),
    COUNTRY_LIST_EMPTY_ERROR("100002", "适用国家不能为空", "适用国家不能为空"),
    ID_NULL_ERROR("100003", "id不能为空", "id不能为空"),
    TYPE_NULL_ERROR("100004", "标签类型不能为空", "标签类型不能为空"),
    SORT_NULL_ERROR("100005", "排序值不能为空", "排序值不能为空"),
    IS_SHOW_ERROR("100006", "是否展示isShow错误", "是否展示isShow错误"),
    GOODS_ID_NULL_ERROR("100007", "商品id不能为空", "商品id不能为空"),
    TAG_TYPE_NULL_ERROR("100008", "标签类型不能为空", "标签类型不能为空"),
    STYLE_TYPE_NULL_ERROR("100009", "样式类型不能为空", "样式类型不能为空"),
    POSITION_NULL_ERROR("100010", "位置不能为空", "位置不能为空"),
    IMG_NULL_ERROR("100011", "标签图片不能为空", "标签图片不能为空"),
    TITLE_NULL_ERROR("100012", "文字不能为空", "文字不能为空"),
    TAG_NAME_REPEAT_ERROR("100013", "标签名称已存在", "标签名称已存在"),
    TAG_INFO_ABSENT("100014", "标签信息不存在", "标签信息不存在"),
    TAG_REL_GOODS_NULL("100015", "标签未关联商品", "标签未关联商品"),
    TAG_REL_TYPE_NULL("100016", "标签关联类型为空", "标签关联类型为空"),
    TAG_REL_GOODS_ID_NULL("100017", "标签关联商品id为空", "标签关联商品id为空"),
    TAG_REL_GOODS_ID_EXCEPTION("100018", "标签关联商品id异常", "标签关联商品id异常"),
    REL_GOODS_TAG_NULL("100019", "商品关联标签为空", "商品关联标签为空"),
    REL_GOODS_TAG_ID_EXCEPTION("100020", "商品关联标签id异常", "商品关联标签id异常"),
    DELETE_REL_GOODS_TAG_EXCEPTION("100021", "需解除商品关联,才可以删除标签", "需解除商品关联,才可以删除标签"),
    TAG_IDS_NULL_ERROR("100022", "商品关联标签为空", "商品关联标签为空"),
    COMBOS_IDS_NULL_ERROR("100023", "id不能为空", "id不能为空"),

    ;

    /**
     * 结果码
     */
    private final String code;
    /**
     * 返回消息
     */
    private final String msg;
    /**
     * 描述
     */
    private final String detail;

    GoodsTagResultCode(String code, String msg, String detail) {
        this.code = code;
        this.msg = msg;
        this.detail = detail;
    }


    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getDetail() {
        return detail;
    }

    @Override
    public String getDetail(String[] arg0) {
        return null;
    }

    @Override
    public String getMsg() {
        return msg;
    }

    @Override
    public String getName() {
        return name();
    }

    @Override
    public String toString() {
        JSONObject object = new JSONObject();
        object.put("code", code);
        object.put("msg", msg);
        return JSON.toJSONString(object);
    }
}
