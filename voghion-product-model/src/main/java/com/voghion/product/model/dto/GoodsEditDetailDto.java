package com.voghion.product.model.dto;

import com.voghion.product.model.po.GoodsExtra;
import com.voghion.product.model.vo.GoodsExtraVO;
import com.voghion.product.model.vo.GoodsManualVO;
import com.voghion.product.model.vo.GoodsPartsVO;
import com.voghion.product.model.vo.PropertyGoodsInfoVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class GoodsEditDetailDto implements Serializable {
    private static final long serialVersionUID = 1536514781224338802L;

    private String oldName;
    private String newName;

    private Long oldCategoryId;
    private Long newCategoryId;

    private String oldItemCode;
    private String newItemCode;

    private Long oldBrandId;
    private Long newBrandId;

    private List<String> oldGoodsImages;
    private List<String> newGoodsImages;

    private List<String> oldGoodsVideos;
    private List<String> newGoodsVideos;

    private Long oldSizeChartTemplateId;
    private Long newSizeChartTemplateId;

    private String oldSizeImage;
    private String newSizeImage;

    private String oldDescription;
    private String newDescription;

    private List<String> oldDetailsImgs;
    private List<String> newDetailsImgs;

    private List<PropertyGoodsInfoVO> oldPropertyGoodsInfoVOS;
    private List<PropertyGoodsInfoVO> newPropertyGoodsInfoVOS;

    private ExtraSnap oldGoodsExtra;
    private ExtraSnap newGoodsExtra;

    //国家商品说明书
    private List<GoodsManualVO> oldGoodsManualVOS;
    private List<GoodsManualVO> newGoodsManualVOS;

    //商品说明视频
    private List<GoodsManualVO> oldGoodsManualVideoList;
    private List<GoodsManualVO> newGoodsManualVideoList;

    private List<GoodsPartsVO> oldGoodsPartsVOS;
    private List<GoodsPartsVO> newGoodsPartsVOS;


    public void listingClear() {
        setOldBrandId(null);
        setNewBrandId(null);
        setOldSizeImage(null);
        setNewSizeImage(null);
        setOldSizeChartTemplateId(null);
        setNewSizeChartTemplateId(null);
    }

//    @Data
//    @AllArgsConstructor
//    public static class PropertyGoodsInfoSnap implements Serializable {
//        private static final long serialVersionUID = 7693287460027608852L;
//
//        //填写类型 1:单选 2:二选一 3:复选 4:自由填写(仅数字) 5:自由填写(仅英文) 6:自由填写(数字+英文)
//        private Integer type;
//
//        private String propertyCnName;
//
//        private List<String> valueList;
//    }

    @Data
    @AllArgsConstructor
    public static class ExtraSnap implements Serializable {
        private static final long serialVersionUID = 7693287460027608852L;

        private String propertyJson;
    }
}
