package com.voghion.product.model.dto;


import com.voghion.product.model.vo.PageParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class GoodsRealShotBlackConfConditionDTO extends PageParam implements Serializable {


    private static final long serialVersionUID = 1L;

    @ApiModelProperty("状态 1:有效 0：无效")
    private Integer status;

    @ApiModelProperty("最后更新人")
    private String updateBy;

    @ApiModelProperty("最后更新时间:start")
    private String startTime;

    @ApiModelProperty("最后更新时间:end")
    private String endTime;

}
