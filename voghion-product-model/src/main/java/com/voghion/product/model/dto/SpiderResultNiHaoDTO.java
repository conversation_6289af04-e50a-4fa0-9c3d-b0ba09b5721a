package com.voghion.product.model.dto;

import com.voghion.product.model.vo.GoodsExtraPropertyVO;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class SpiderResultNiHaoDTO implements Serializable {
    private static final long serialVersionUID = 6091970767266831512L;
    private String name;
    private String mainImage;
    private BigDecimal minPrice;
    private BigDecimal maxPrice;
    private List<String> detailsImgs;
    private List<String> goodsImages;
    private String productId;
    private Long categoryId;
    private String goodsUrl;
    private Long shopId;
    private String shopName;
    private String vedio;
    private String goodsMoveId;
    private String description;
    private List<GoodsExtraPropertyVO> specification;
    private List<SpiderGoodsSkuPropNiHaoDTO> skuProps;
    private List<SpiderGoodsSkuNiHaoDTO> skuList;

}
