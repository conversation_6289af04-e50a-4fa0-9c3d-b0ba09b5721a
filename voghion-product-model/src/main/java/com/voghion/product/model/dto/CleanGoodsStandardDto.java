package com.voghion.product.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/31 17:53
 */
@Data
public class CleanGoodsStandardDto implements Serializable {

    /**
     * 最小id
     */
    private Long minId;

    /**
     * 类目id
     */
    private List<Long> categoryIds;

    private List<Long> goodsIdsList;

    /**
     * 是否去重
     */
    private boolean duplicateRemoval;

    /**
     * 计数清零
     */
    private boolean countResetZero;

}
