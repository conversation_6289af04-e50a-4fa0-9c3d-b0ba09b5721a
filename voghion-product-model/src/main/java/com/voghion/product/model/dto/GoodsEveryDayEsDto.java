package com.voghion.product.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @description:
 * @date: 2022/9/28 上午10:16
 * @author: jashley
 */
@Data
public class GoodsEveryDayEsDto implements Serializable {
    private static final long serialVersionUID = 285992524599567843L;

    /**
     * uv点击率
     */
    private BigDecimal clickRateUv;
    /**
     * 创建时间
     */
    private Date createDay;
    /**
     * 成交转化率
     */
    private BigDecimal dealRateUv;
    /**
     * 千次曝光转化
     */
    private BigDecimal goodsShowGmv;
    /**
     * 叶子类目千次曝光转化率
     */
    private BigDecimal lastShowGmv;
    /**
     * 商品退款率
     */
    private BigDecimal refundRateReal;
    /**
     * 店铺退款率 sum(refund_cnt)/sum(deal_cmt) 暂定 shop_refund_rate
     */
    private BigDecimal shopRefundRateReal;
    /**
     * 覆盖范围(1，3，5,7，15，30天)
     */
    private Integer runDays;
    /**
     * 店铺复购率
     */
    private BigDecimal shopRepayRate;

    private Long goodsId;


    private Long dealCnt;

    private Float addUv;

    private Float showGoodsUv;

//    private String goodsName;
//    private BigDecimal shopId;
//    private String shopName;
//    private BigDecimal categoryId;
//    private String categoryName;
//
//    private BigDecimal price;
//    private BigDecimal maxPrice;
//    private BigDecimal minPrice;
//
//    private BigDecimal addUv;
//    private BigDecimal addtocartconfirmGoodsPv;
//    private String aimName;
//    private BigDecimal buynowconfirmGoodsPv;
//    private BigDecimal clickPv;
//    private BigDecimal clickRatePv;
//    private BigDecimal clickUv;
//
//    private BigDecimal dealCnt;
//    private BigDecimal dealNum;
//    private BigDecimal dealPayMoney;
//    private BigDecimal dealRate;
//    private BigDecimal dealUv;
//
//    private BigDecimal lastClickRatePv;
//    private BigDecimal lastDealClickRatePv;
//
//    private BigDecimal orderCnt;
//    private BigDecimal orderMoney;
//    private BigDecimal orderNum;
//    private BigDecimal orderUv;
//    private BigDecimal refundCnt;
//    private BigDecimal refundMoney;
//    private BigDecimal refundNum;
//
//    private BigDecimal shopShowGmv;
//    private BigDecimal showGoodsPv;
//    private BigDecimal showGoodsUv;
//    private BigDecimal showPv;
//    private BigDecimal showPvDe200;
    /**
     * 曝光人数
     */
    private BigDecimal showUv;
//    private BigDecimal unitPerOrder;
//
//
//    private BigDecimal firstCategoryId;
//    private String firstCategoryName;
//    private BigDecimal firstClickRatePv;
//    private BigDecimal firstDealClickRatePv;
//    private BigDecimal firstShowGmv;
//
//    private BigDecimal secondCategoryId;
//    private String secondCategoryName;
//    private BigDecimal secondClickRatePv;
//    private BigDecimal secondDealClickRatePv;
//    private BigDecimal secondShowGmv;
//
//    private BigDecimal thirdCategoryId;
//    private String thirdCategoryName;
//    private BigDecimal thirdClickRatePv;
//    private BigDecimal thirdDealClickRatePv;
//    private BigDecimal thirdShowGmv;


    public BigDecimal getRefundRateReal() {
        return refundRateReal == null ? BigDecimal.ZERO : refundRateReal;
    }

    public BigDecimal getShopRefundRateReal() {
        return shopRefundRateReal == null ? BigDecimal.ZERO : shopRefundRateReal;
    }
}
