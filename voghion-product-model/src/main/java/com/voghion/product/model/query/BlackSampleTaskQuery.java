package com.voghion.product.model.query;

import com.colorlight.base.model.PageView;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @Description 黑样本任务请求
 * <AUTHOR>
 * @Date 2025/4/18
 **/
@Data
@ApiModel(value = "黑样本任务请求")
public class BlackSampleTaskQuery{

    @ApiModelProperty("商品id (换行分隔)")
    private String goodsIdListStr;

    @ApiModelProperty(value = "类目ID")
    private Long categoryId;

    @ApiModelProperty(value = "审核状态 1待审核 2已审核 3已关闭")
    private Integer auditStatus;

    @ApiModelProperty(value = "开始审核时间")
    private Date startAuditTime;

    @ApiModelProperty(value = "结束审核时间")
    private Date endAuditTime;

    @ApiModelProperty(value = "开始创建时间")
    private Date startCreateTime;

    @ApiModelProperty(value = "结束创建时间")
    private Date endCreateTime;

    @ApiModelProperty(value = "品牌名称")
    private String brandName;

    @ApiModelProperty(value = "同盾审核结果 0驳回 1通过")
    private Integer tongdunAuditResult;

    @ApiModelProperty(value = "自研审核结果 0驳回 1通过")
    private Integer selfAuditResult;

    @ApiModelProperty("是否展示所有任务 1是 0否")
    private Integer showType = 0;

    @ApiModelProperty("审核人name")
    private String auditUserName;

    private Integer pageNow = 1;
    private Integer pageSize = 20;

}
