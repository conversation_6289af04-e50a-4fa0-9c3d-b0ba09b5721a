package com.voghion.product.model.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class AliWeighModel {
    @ExcelProperty(value = "末级类目id", index = 0)
    private Long lastCategoryId;

    @ExcelProperty(value = "类目id", index = 6)
    private Long secondCategoryId;

    @ExcelProperty(value = "物流属性", index = 19)
    private Integer logicCode;

    @ExcelProperty(value = "默认重量", index = 20)
    private BigDecimal defaultWeigh;
}
