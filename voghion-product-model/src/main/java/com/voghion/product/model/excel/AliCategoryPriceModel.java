package com.voghion.product.model.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class AliCategoryPriceModel {
    /**
     * 二级类目ID
     */
    @ExcelProperty(value = "末级类目id", index = 0)
    private Long lastCategoryId;

    /**
     * 二级类目ID
     */
    @ExcelProperty(value = "类目id", index = 6)
    private Long secondCategoryId;

    /**
     * 起始价格
     */
    @ExcelProperty(value = "起始价格", index = 21)
    private BigDecimal startPrice;

    /**
     * 结束价格
     */
    @ExcelProperty(value = "结束价格", index = 22)
    private BigDecimal endPrice;


    /**
     * 毛利率
     */
    @ExcelProperty(value = "毛利率", index = 23)
    private String profitRate;

    /**
     * 退款率
     */
    @ExcelProperty(value = "退款率", index = 24)
    private String refundRate;

    /**
     * 国内运费
     */
    @ExcelProperty(value = "国内运费", index = 25)
    private BigDecimal freight;

    /**
     * 利润
     */
    @ExcelProperty(value = "利润", index = 26)
    private BigDecimal profit;

}
