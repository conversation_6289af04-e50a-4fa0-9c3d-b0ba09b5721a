package com.voghion.product.model.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class GoodsVATConfigVO {
    /**
     * 自增id
     */
    @ExcelProperty(value = "自增id", index = 0)
    private Long id;

    /**
     * 商品id
     */
    @ExcelProperty(value = "商品id", index = 1)
    private Long goodsId;

    /**
     * 商品低价
     */
    @ExcelProperty(value = "德国商品低价（含vat）", index = 2)
    private BigDecimal minPrice;

    /**
     * 商品高价
     */
    @ExcelProperty(value = "德国商品高价（含vat）", index = 3)
    private BigDecimal maxPrice;


    /**
     * 商品低价
     */
    @ExcelProperty(value = "德国商品低价（不含vat）", index = 4)
    private BigDecimal DEMinPrice;

    /**
     * 商品高价
     */
    @ExcelProperty(value = "德国商品高价（不含vat）", index = 5)
    private BigDecimal DEMaxPrice;

    /**
     * 单日支付单数
     */
    @ExcelProperty(value = "单日支付单数", index = 6)
    private Long currentDayPayCount;

    /**
     * 昨日支付单数
     */
    @ExcelProperty(value = "昨日支付单数", index = 7)
    private Long yesterdayPayCount;

    /**
     * 商品主图
     */
    @ExcelProperty(value = "商品主图", index = 8)
    private String mainImage;

    /**
     * 类目id
     */
    @ExcelProperty(value = "类目id", index = 9)
    private Integer categoryId;

    /**
     * 上一次的vat税率
     */
    @ExcelProperty(value = "上一次的vat税率", index = 10)
    private BigDecimal beforeVat;

    /**
     * vat税率
     */
    @ExcelProperty(value = "vat税率", index = 11)
    private BigDecimal vat;

    /**
     * 修正状态 1 - 修正过  0 - 未修正
     */
    @ExcelProperty(value = "修正状态 1 - 修正过  0 - 未修正", index = 12)
    private Integer changeStatus;

    /**
     * 生效状态 1 - 有效 0 - 无效
     */
    @ExcelProperty(value = "生效状态 1 - 有效 0 - 无效", index = 13)
    private Integer effectStatus;

    /**
     * 店铺id
     */
    @ExcelProperty(value = "店铺id店铺id", index = 14)
    private Long shopId;

    /**
     * 店铺名称
     */
    @ExcelProperty(value = "店铺名称", index = 15)
    private String shopName;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间", index = 16)
    private String createTime;

    /**
     * 更新时间
     */
    @ExcelProperty(value = "更新时间", index = 17)
    private String updateTime;

    /**
     * 负责小二
     */
    @ExcelProperty(value = "负责小二", index = 18)
    private String principal;


    @ExcelProperty(value = "一级类目", index = 19)
    private String categoryName1;

    @ExcelProperty(value = "二级类目", index = 20)
    private String categoryName2;

    @ExcelProperty(value = "三级类目", index = 21)
    private String categoryName3;

    @ExcelProperty(value = "四级类目", index = 22)
    private String categoryName4;

}
