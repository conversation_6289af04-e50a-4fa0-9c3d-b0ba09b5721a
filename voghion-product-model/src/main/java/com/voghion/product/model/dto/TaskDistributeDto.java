package com.voghion.product.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.util.List;

@Getter
@Setter
public class TaskDistributeDto {
    private Long goodsId;
    private Integer level;
    private String auditor;
    /**
     * 申诉原因
     */
    private String appealReason;

    /**
     * 申诉内容
     */
    private String appealContent;

    /**
     * 申诉图片
     */
    private List<String> appealImages;

    /**
     * 品牌库id
     */
    private Long brandId;

    /**
     * 举报记录ID
     */
    private Integer reportId;

    public  TaskDistributeDto (){}

    public  TaskDistributeDto (Long goodsId, Integer level, String auditor){
        this.goodsId = goodsId;
        this.level = level;
        this.auditor = auditor;
    }

    public  TaskDistributeDto (Long goodsId, Integer level, String auditor, String appealReason, String appealContent, List<String> appealImages){
        this.goodsId = goodsId;
        this.level = level;
        this.auditor = auditor;
        this.appealReason = appealReason;
        this.appealContent = appealContent;
        this.appealImages = appealImages;
    }

    public  TaskDistributeDto (Long goodsId, Integer level, String auditor, Integer reportId){
        this.goodsId = goodsId;
        this.level = level;
        this.auditor = auditor;
        this.reportId = reportId;
    }

}
