package com.voghion.product.model.enums;

import com.google.common.collect.Lists;

import java.util.List;

public enum GoodsIsShowEnums {

    TEMPLATE(20, "模板样例商品"),
    TAKE_OFF(0, "下架"),
    SHELF(1, "上架"),
    WAIT_AUDIT(2, "待确认"),
    PROHIBIT(3, "禁售"),
    SOLD_OUT(4, "售罄"),
    AUDITING(5, "审核中"),
    AUDIT_REJECT(6, "同盾审核驳回"),
    AUDIT_BY_PICKED_SHOP(7, "小二审核中"),
    ADJUST_WITH_PRICE(8, "待调整价格相关"),
    ADJUST_NOT_PRICE(9, "待调整非价格相关"),

    FREEZE(21, "商品冻结"),

    ;

    private final Integer type;
    private final String desc;

    GoodsIsShowEnums(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return this.type;
    }

    public String getDesc() {
        return this.desc;
    }

    public static String getDescByType(Integer type) {
        for (GoodsIsShowEnums value : GoodsIsShowEnums.values()) {
            if (value.getType().equals(type)) {
                return value.getDesc();
            }
        }
        return null;
    }

    public static String getDescByType(String type) {
        return getDescByType(Integer.parseInt(type));
    }

    public static List<Integer> getCommonShowTypes() {
        return Lists.newArrayList(0, 1, 2, 3, 4, 5, 6, 7, 8, 9);
    }

    public static List<String> getShopProhibitShowTypes() {
        return Lists.newArrayList("100", "101", "102", "103", "104", "105", "106", "107", "108", "109");
    }

    public static List<String> getShopHolidayShowTypes() {
        return Lists.newArrayList("200", "201", "202", "203", "204", "205", "206", "207", "208", "209");
    }
}
