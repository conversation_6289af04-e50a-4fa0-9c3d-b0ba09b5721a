package com.voghion.product.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
public class PropertyValueNewModel implements Serializable {


    private Long id;

    //属性值
    private String value;

    private Long propertyId;

    //类目ID，冗余字段
    private Long categoryId;

    //排序
    private Integer sort;

    //创建时间
    private LocalDateTime createTime;

    //状态 1表示正常 99表示禁用
    private Integer status;

    //原始属性值
    private String originalValue;

    //是否已翻译，0:未翻译，1:已翻译
    private Integer translated;

    //图片链接
    private String imgUrl;

    //大图链接
    private String mainUrl;


}
