package com.voghion.product.model.enums;

public enum GoodsPriceReductionEnums {
    PRICE_REFUSE(-1,"商家已拒绝"),
    SHUT_DOWN(0, "关闭"),
    TO_BE_ADJUSTED(1, "待商家调整"),
    PRICE_REDUCED(2, "商家已降价"),
    SHOP_RETURN(3, "商家主动清退"),
    SYSTEM_RETURN(4, "系统强制清退"),
    ;

    private Integer code;
    private String desc;

    GoodsPriceReductionEnums(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static String getDescByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (GoodsPriceReductionEnums value : values()) {
            if (value.getCode().equals(code)) {
                return value.getDesc();
            }
        }
        return null;
    }
}
