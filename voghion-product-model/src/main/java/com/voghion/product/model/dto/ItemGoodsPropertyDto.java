package com.voghion.product.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/9 16:45
 */
@Data
public class ItemGoodsPropertyDto implements Serializable {

    @ApiModelProperty("商品规格名称(别名)")
    private String name;

    @ApiModelProperty("商品规格名称排序")
    private Integer sort;

    @ApiModelProperty("是否为主规格")
    private boolean mainProperty;

    @ApiModelProperty("商品规格值信息")
    private List<ItemGoodsPropertyValueDto> values;

}
