package com.voghion.product.model.dto;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class GoodsRealShotConfDTO implements Serializable {


    private static final long serialVersionUID = 1L;


    @ApiModelProperty("id")
    private Long id;
    /**
     * 配置维度：1:店铺维度 2:类目维度
     * @see com.voghion.product.model.enums.DimensionTypeEnum
     */
    @ApiModelProperty("配置维度")
    private Integer dimensionType;

    @ApiModelProperty("卖家id，多个用换行分隔")
    private String shopIds;

    @ApiModelProperty("类目id，多个用换行分隔")
    private String categoryIds;

    /**
     * 商家可配
     */
    @ApiModelProperty("卖家可配")
    private Boolean sellerConfigurable;

    /**
     * 是否必填
     */
    @ApiModelProperty("是否必传")
    private Boolean required;

    /**
     * app 可见
     */
    @ApiModelProperty("app 可见")
    private Boolean appVisible;

}
