package com.voghion.product.model.enums;

/**
 * @description:
 * @date: 2022/11/24 下午5:23
 * @author: jashley
 */
public enum ChanceGoodsTemplateStatusEnums {
    DRAFT(0, "草稿"),
    OPEN(1, "开放"),
    AUTO_CLOSE(2, "自动关闭"),
    MANUAL_CLOSE(3, "人工关闭"),
    ;

    private final Integer code;

    private final String desc;

    ChanceGoodsTemplateStatusEnums(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static String getNameByCode(Integer code) {
        if (code == null) {
            return "";
        }

        for (ChanceGoodsTemplateStatusEnums value : ChanceGoodsTemplateStatusEnums.values()) {
            if (value.getCode().equals(code)) {
                return value.getDesc();
            }
        }
        return "";
    }


}
