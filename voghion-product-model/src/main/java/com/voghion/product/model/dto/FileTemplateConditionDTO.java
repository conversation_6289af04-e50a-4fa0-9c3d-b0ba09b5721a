package com.voghion.product.model.dto;

import com.voghion.product.model.vo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel
public class FileTemplateConditionDTO extends PageParam {

    private static final long serialVersionUID = -9193508306046794360L;

    /**
     * 模版文件描述
     */
    @ApiModelProperty(value = "文件描述")
    private String fileName;

    /**
     * 模版链接
     */
    @ApiModelProperty(value = "文件链接")
    private String fileUrl;


    /**
     * 模版id
     */
    @ApiModelProperty(value = "模版ID")
    private Long id;

}
