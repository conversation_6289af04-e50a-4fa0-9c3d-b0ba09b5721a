package com.voghion.product.model.enums;

import java.util.ArrayList;
import java.util.List;

public enum BuyOnePieceEnums {
    TO_BE_REVIEWED(0, "待审核"),
    IN_PROGRESS(1, "进行中"),
    OVERRULE(2, "驳回"),
    PAUSING(3, "暂停中"),
    ENDED(4, "已停止"),
    ;
    private Integer code;
    private String desc;

    BuyOnePieceEnums(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static List<Integer> getValidStatus() {
        ArrayList<Integer> res = new ArrayList<>();
        res.add(TO_BE_REVIEWED.getCode());
        res.add(IN_PROGRESS.getCode());
        res.add(PAUSING.getCode());
        return res;
    }

    public static String getDescByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (BuyOnePieceEnums value : values()) {
            if (value.getCode().equals(code)) {
                return value.getDesc();
            }
        }
        return null;
    }

    public static BuyOnePieceEnums getBuyOnePieceEnumByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (BuyOnePieceEnums onePieceEnums : BuyOnePieceEnums.values()) {
            if (onePieceEnums.getCode().equals(code)) {
                return onePieceEnums;
            }
        }
        return null;
    }

    public static List<Integer> getApprovedState(Integer code) {
        List<Integer> res = new ArrayList<>();
        if (code == null) {
            return res;
        }
        BuyOnePieceEnums onePieceEnum = BuyOnePieceEnums.getBuyOnePieceEnumByCode(code);
        if (onePieceEnum == null) {
            return res;
        }
        switch (onePieceEnum) {
            case TO_BE_REVIEWED:
                res.add(ENDED.getCode());
                res.add(IN_PROGRESS.getCode());
                res.add(OVERRULE.getCode());
                break;
            case IN_PROGRESS:
                res.add(ENDED.getCode());
                res.add(PAUSING.getCode());
                res.add(OVERRULE.getCode());
                break;
            case OVERRULE:
                break;
            case PAUSING:
                res.add(ENDED.getCode());
                res.add(IN_PROGRESS.getCode());
                res.add(OVERRULE.getCode());
                break;
            case ENDED:
                break;
            default:
        }
        return res;
    }
}
