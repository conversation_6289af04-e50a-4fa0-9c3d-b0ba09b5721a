package com.voghion.product.model.dto;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class GoodsEditPropertyDto implements Serializable {
    private static final long serialVersionUID = 1536514781224338802L;

    private List<PropertyDTO> oldPropertyList;

    private List<PropertyDTO> newPropertyList;

    private List<GoodsItemDTO> oldSkuList;

    private List<GoodsItemDTO> newSkuList;


//    @Data
//    public static class PropertySnap implements Serializable {
//        private static final long serialVersionUID = 3716759358134273863L;
//
//        private Long id;
//
//        private String name;
//
//        private List<PropertyValueSnap> propertyValueList;
//
//        public PropertySnap(Long id, String name) {
//            this.id = id;
//            this.name = name;
//        }
//    }

//    @Data
//    @AllArgsConstructor
//    public static class PropertyValueSnap implements Serializable {
//        private static final long serialVersionUID = 3716759358134273863L;
//
//        private Long id;
//
//        private String value;
//
//        private String imgUrl;
//    }
//
//    @Data
//    @AllArgsConstructor
//    public static class SkuSnap implements Serializable {
//        private static final long serialVersionUID = 3716759358134273863L;
//
//        private Long id;
//
//        private String name;
//
//        private BigDecimal originalPrice;
//
//        private String skuImage;
//    }
}
