package com.voghion.product.model.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class GoodsVATRecordModel {
    // 日期
    @ExcelProperty(value = "日期", index = 0)
    private String recordDate;

    // 当日支付单数
    @ExcelProperty(value = "当日支付单数", index = 1)
    private Long currentDayPayCount;

    // 单数环比增减值
    @ExcelProperty(value = "单数环比增减值", index = 2)
    private Long payCountChange;

    // 单数环比增减比列
    @ExcelProperty(value = "单数环比增减比列", index = 3)
    private String changePercent;

    // 当日vat税率
    @ExcelProperty(value = "当日vat税率", index = 4)
    private BigDecimal vat;
}
