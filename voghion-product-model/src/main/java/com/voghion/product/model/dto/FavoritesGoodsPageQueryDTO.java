package com.voghion.product.model.dto;

import com.voghion.product.model.vo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
@ApiModel
public class FavoritesGoodsPageQueryDTO extends PageParam {
    /**
     * 自增id
     */
    @ApiModelProperty(value = "收藏夹id")
    private Long favoritesId;
    @ApiModelProperty(value = "商品id")
    private String goodsIds;
    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    // 0 - 下架  1 - 上架
    @ApiModelProperty(value = "商品状态 0 - 下架  1 - 上架")
    private String isShow;

    // 0 - 删除  1 - 正常
    @ApiModelProperty(value = "收藏商品状态 0 - 删除  1 - 正常")
    private Integer favoriteStatus;
    @ApiModelProperty(value = "店铺店名")
    private String shopName;
    @ApiModelProperty(value = "商品创建开始时间")
    private String createStartTime;
    @ApiModelProperty(value = "商品创建结束时间")
    private String createEndTime;
    @ApiModelProperty(value = "商品添加人")
    private String createUserName;

    // 活动类型  1 - flashdeal 2 - 满减 3 - 运营选品 4 - 七日达
    @ApiModelProperty(value = "报名活动类型  1 - flashdeal 2 - 满减 3 - 运营选品 4 - 七日达")
    private Integer signUpActivityType;

    // 活动类型  1 - flashdeal 2 - 满减 3 - 运营选品 4 - 七日达
    @ApiModelProperty(value = "参加活动类型  1 - flashdeal 2 - 满减 3 - 运营选品 4 - 七日达")
    private Integer joinActivityType;

    // 活动类型  1 - flashdeal 2 - 满减 3 - 运营选品 4 - 七日达
    @ApiModelProperty(value = "未报名活动类型  1 - flashdeal 2 - 满减 3 - 运营选品 4 - 七日达")
    private Integer notSignupActivityType;

    // 活动类型  1 - flashdeal 2 - 满减 3 - 运营选品 4 - 七日达
    @ApiModelProperty(value = "未参加活动类型  1 - flashdeal 2 - 满减 3 - 运营选品 4 - 七日达")
    private Integer notJoinActivityType;

    // 当前报名活动
    @ApiModelProperty(value = "当前报名活动名称")
    private String signUpActivityName;

    // 当前参加活动
    @ApiModelProperty(value = "当前参加活动名称")
    private String joinActivityName;

    // 未报名活动名称
    @ApiModelProperty(value = "未报名活动名称")
    private String notSignupActivityName;

    // 未参与活动名称
    @ApiModelProperty(value = "未参与活动名称")
    private String notJoinActivityName;

    // 类目id
    @ApiModelProperty(value = "类目id")
    private Long categoryId;

    // 最高价开始
    @ApiModelProperty(value = "maxPriceBegin")
    private BigDecimal maxPriceBegin;

    // 最高价结束
    @ApiModelProperty(value = "最高价结束")
    private BigDecimal maxPriceEnd;

    // 最低价开始
    @ApiModelProperty(value = "最低价开始")
    private BigDecimal minPriceBegin;

    // 最低价结束
    @ApiModelProperty(value = "最低价结束")
    private BigDecimal minPriceEnd;

    /**
     * 锁定标签类型
     * @see com.voghion.product.model.enums.GoodsLockLabelTypEnums
     */
    @ApiModelProperty(value = "锁定标签类型 1 投放 2 测款 3 热卖 4 flashDeal 5 运营选品 6 满减大促 7 七日达")
    private Integer lockLabelType;

    // 近30天gmv开始
    @ApiModelProperty(value = "近30天gmv开始")
    private BigDecimal gmvStart;

    // 近30天gmv结束
    @ApiModelProperty(value = "近30天gmv结束")
    private BigDecimal gmvEnd;

    // 备注
    @ApiModelProperty(value = "备注")
    private String remarks;



}
