package com.voghion.product.model.enums;

/**
 * <AUTHOR>
 * @date 2024/9/25
 */
public enum ArrivalStatusEnums {

    OVERRULE(-1, "已驳回"),
    TO_BE_DISPOSED(0, "待处理"),
    DISPOSED(1, "已处理"),
    TO_BE_ALLOCATED(2, "待分配"),
    ;

    private Integer code;
    private String desc;

    ArrivalStatusEnums(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
